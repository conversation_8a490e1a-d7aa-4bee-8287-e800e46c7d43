Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.56f1c1 (bb6410cfcc53) revision 12280848'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32598 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/UnityPro/AIHuman/chat-aihuman_Inmo
-logFile
Logs/AssetImportWorker1.log
-srvPort
65241
Successfully changed project path to: E:/UnityPro/AIHuman/chat-aihuman_Inmo
E:/UnityPro/AIHuman/chat-aihuman_Inmo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [35876]  Target information:

Player connection [35876]  * "[IP] ********* [Port] 0 [Flags] 2 [Guid] 1634098795 [EditorId] 1634098795 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-T2O84AK) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [35876] Host joined multi-casting on [***********:54997]...
Player connection [35876] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
Refreshing native plugins compatible for Editor in 11.04 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.56f1c1 (bb6410cfcc53)
[Subsystems] Discovering subsystems at path D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/UnityPro/AIHuman/chat-aihuman_Inmo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3070 (ID=0x2484)
    Vendor:   NVIDIA
    VRAM:     8018 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56524
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.003819 seconds.
- Loaded All Assemblies, in  0.185 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 696 ms
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.875 seconds
Domain Reload Profiling: 1061ms
	BeginReloadAssembly (53ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (80ms)
		LoadAssemblies (53ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (78ms)
			TypeCache.Refresh (77ms)
				TypeCache.ScanAssembly (69ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (849ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (753ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (67ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.575 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Failed to load: D:/soft/Unity/2022.3.56f1c1/2022.3.56f1c1/Editor/Data/PlaybackEngines/VisionOSPlayer\x64\UnityEditor.VisionOS.Native.dll
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 1189ms
	BeginReloadAssembly (76ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (13ms)
	RebuildCommonClasses (17ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (461ms)
		LoadAssemblies (269ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (196ms)
				TypeCache.ScanAssembly (180ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
Shader 'XM/Mobile/MirrorReflection': fallback shader 'Universal Render Pipeline/Lit' not found
Shader 'XM/Mobile/MirrorReflection': fallback shader 'Universal Render Pipeline/Lit' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 7.97 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8544 Unused Serialized files (Serialized files now loaded: 0)
Unloading 113 unused Assets / (201.0 KB). Loaded Objects now: 8996.
Memory consumption went from 246.6 MB to 246.4 MB.
Total: 3.501600 ms (FindLiveObjects: 0.346800 ms CreateObjectMapping: 0.158600 ms MarkObjects: 2.821000 ms  DeleteObjects: 0.174100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Import Request.
  Time since last request: 111813.339887 seconds.
  path: Assets/chat-aihuman/Runtime/Art/Shader/MobileMirrorReflection.shader
  artifactKey: Guid(98d27d35a28b3bd4bbb36cad3f099336) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/chat-aihuman/Runtime/Art/Shader/MobileMirrorReflection.shader using Guid(98d27d35a28b3bd4bbb36cad3f099336) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
Shader 'XM/Mobile/MirrorReflection': fallback shader 'Universal Render Pipeline/Lit' not found
 -> (artifact id: '1c2f238abfee2b077e22b129c80fdace') in 0.007329 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/chat-aihuman/Runtime/Art/Shader/MirrorReflection_README.md
  artifactKey: Guid(7d023a53bf96a0c4aaf0c2dfbe7847cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/chat-aihuman/Runtime/Art/Shader/MirrorReflection_README.md using Guid(7d023a53bf96a0c4aaf0c2dfbe7847cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: '8721bb698cdc16ba68ea4795cd6b97d8') in 0.000671 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
