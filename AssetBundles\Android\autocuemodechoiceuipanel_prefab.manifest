ManifestFileVersion: 0
CRC: 1502800738
Hashes:
  AssetFileHash:
    serializedVersion: 2
    Hash: b5b378d39d50bbda074a6b7f7b703255
  TypeTreeHash:
    serializedVersion: 2
    Hash: 9dc0575d030285560e87832459e6d4a4
  IncrementalBuildHash:
    serializedVersion: 2
    Hash: b5b378d39d50bbda074a6b7f7b703255
HashAppended: 0
ClassTypes:
- Class: 1
  Script: {instanceID: 0}
- Class: 21
  Script: {instanceID: 0}
- Class: 28
  Script: {instanceID: 0}
- Class: 48
  Script: {instanceID: 0}
- Class: 65
  Script: {instanceID: 0}
- Class: 114
  Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: e6e8303861717324bb3d08a39fc878ee, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fea91cbfec6e0404aba4918f9a1fcb6b, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 141109fb25f0083469e71ce8cb3e75c8, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: b77990ae38d663e4eb9fc6a1a6b5d219, type: 3}
- Class: 115
  Script: {instanceID: 0}
- Class: 128
  Script: {instanceID: 0}
- Class: 213
  Script: {instanceID: 0}
- Class: 222
  Script: {instanceID: 0}
- Class: 224
  Script: {instanceID: 0}
- Class: 225
  Script: {instanceID: 0}
- Class: 687078895
  Script: {instanceID: 0}
SerializeReferenceClassIdentifiers:
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Circle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.GradientEffect
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Hexagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.NStarPolygon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Pentagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Rectangle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Triangle
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.Events.PersistentCallGroup
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.FontData
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
Assets:
- Assets/chat-aihuman/Runtime/Prefab/UI/ARAutocue/AutocueModeChoiceUIPanel.prefab
Dependencies: []
