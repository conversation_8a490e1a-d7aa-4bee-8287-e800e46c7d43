using System.Collections.Generic;
using System.Text.RegularExpressions;
using LitJson;
using QFramework;
using Unity.VisualScripting;
using UnityEditor;
using UnityEngine;
using XM.Common;

namespace XM.Core 
{
    public class AIChatSystem : AbstractSystem
    {
        private string defaultAppCode = "2561616180091249059";
        private Queue<GetSSEInfo> SSEInfoQueue = new Queue<GetSSEInfo>();

        private List<long> talkInfoDataList = new List<long>();

        private Dictionary<long, TalkInfoData> sseDicInfo = new Dictionary<long, TalkInfoData>();

        /// <summary>
        /// 智能体状态流程子集
        /// </summary>
        private Dictionary<AIAgentStateEnum, AIAgentStateProcessItem> aiAgentStateDic;

        private bool isPlaySTT = false;


        private Queue<string> SSTQueue = new Queue<string>();

        private string delayContent;


        private float delayAddSEEContentTime = 0.5f;

        /// <summary>
        /// 延迟时间Id
        /// </summary>
        private int delayTimerId = 0;


        public VAD _vad;

        public PCMToWAV _ptw;

        protected override void OnInit()
        {
        }

        public void InitSystem() 
        {
            _vad = GameObject.Find("VAD").GetComponent<VAD>();
            _ptw = _vad.GetComponent<PCMToWAV>();
            if (SSEInfoQueue.Count != 0)
                SSEInfoQueue.Clear();

            if (aiAgentStateDic == null) 
            {
                aiAgentStateDic = new Dictionary<AIAgentStateEnum, AIAgentStateProcessItem>()
                {
                    [AIAgentStateEnum.Sleep] = new AIAgentStateProcessItem_Sleep(),
                    [AIAgentStateEnum.WakeWord] = new AIAgentStateProcessItem_WakeWord(),
                    [AIAgentStateEnum.Question] = new AIAgentStateProcessItem_Question(),
                    [AIAgentStateEnum.Answer] = new AIAgentStateProcessItem_Answer(),
                    [AIAgentStateEnum.WaitTalk] = new AIAgentStateProcessItem_WaitTalk(),
                };
            }
        }

        public void OnUpdate() 
        {
            if (SSEInfoQueue.Count != 0)
            {
                GetSSEInfo info = SSEInfoQueue.Dequeue();
                GetSTTVoice(info);
            }

            if (talkInfoDataList.Count != 0)
            {
                TalkInfoData infoData = sseDicInfo[talkInfoDataList[0]];
                if (!this.GetModel<ChatModel>().IsReplyText.Value)
                {
                    if (infoData.CheckIsTalk())
                    {
                        talkInfoDataList.RemoveAt(0);
                        PlayChatText(infoData);
                    }
                }
                else
                {
                    if (infoData.CheckIsShowText())
                    {
                        talkInfoDataList.RemoveAt(0);
                        PlayChatText(infoData);
                    }
                }
            }
        }

        public void StartNewConversation(string conversationName)
        {
            string endpoint = $"{URLData.URLRootPath}{URLData.URLCreateConversationPath}";
            if (!string.IsNullOrEmpty(AIArchitecture.Interface.GetModel<UserModel>().CurrentAIAgentCode))
                defaultAppCode = AIArchitecture.Interface.GetModel<UserModel>().CurrentAIAgentCode;
            //构建请求体JSON
            string jsonBody = $@"{{
            ""appCode"": ""{defaultAppCode}"",
            ""createFlag"": 0,
            ""name"": ""{conversationName}""
            }}";
            HttpRequestManager.Instance.Post(
                endpoint, jsonBody, (response) =>
                {
                    if (response != null)
                    {
                        string info = System.Text.Encoding.UTF8.GetString(response.downloadHandler.data);
                        var result = JsonMapper.ToObject<ConversationCreateResponse>(info);
                        if (result.success)
                        {
                           TypeEventSystem.Global.Send(new StartNewConversationResultEvent() { isSuc = true, conversationId = result.data.code });
                        }
                        else
                        {
                            TypeEventSystem.Global.Send(new StartNewConversationResultEvent() { isSuc = false, msg = result.msg });
                        }
                    }
                    else
                    {
                        TypeEventSystem.Global.Send(new StartNewConversationResultEvent() { isSuc = false, msg = "response Is Null" });
                    }
                },
                (URLConstant.URLContentType, URLConstant.URLContentTypeValueApplicaitonJson),
                (URLConstant.URLCookie, this.GetModel<UserModel>().UserCookie),
                (URLConstant.URLAccessToken, this.GetModel<ChatModel>().AccessTokenResponse.data.accessToken)
               );
        }


        #region 发送给智能体消息
        public void SendMessageToAgent(string prompt, string conversationCode)
        {
            string endpoint = $"{URLData.URLRootPath}{URLData.URLSendMessageToAgentPath}";

            var requestBody = new StreamRequestBody
            {
                requestTaskNo = System.Guid.NewGuid().ToString("N"),
                appCode = defaultAppCode,
                conversationCode = conversationCode,
                instruct = "",
                content = prompt,
                enableReasoning = false,
                variateMap = new VariateMap(),
            };

            string jsonBody = JsonMapper.ToJson(requestBody);
            jsonBody = StringTools.TransitionJsonFormat(jsonBody);

            HttpRequestManager.Instance.PostRequestToStringAsycnTaskToken(endpoint, jsonBody,
                this.GetModel<ChatModel>().AccessTokenResponse.data.accessToken, PostRequestToStringAsycnTaskSuccuss, PostRequestToStringAsycnTaskFail);
        }

        /// <summary>
        /// 智能体模型接收消息事件
        /// </summary>
        private void PostRequestToStringAsycnTaskSuccuss(string info)
        {
            try
            {
                info = Regex.Replace(info, @"\s+", "");
                XMDebug.Log($"PostRequestToStringAsycnTaskSuccuss {info} len {info.Length}");
                if (!string.IsNullOrEmpty(info))
                {
                    string resultStr = info.Contains("data")?info.Split("data:")[1] : info;

                    string fin = resultStr;
                    JsonData jsonDataInfo = JsonMapper.ToObject(fin);
                    XMDebug.Log(this, $"PostRequestToStringAsycnTaskSuccuss fin {fin}");

                    if (string.IsNullOrEmpty(fin))
                    {
                        XMDebug.Log(this, $"PostRequestToStringAsycnTaskSuccuss data base is Null");
                        return;
                    }
                    if (jsonDataInfo.Keys.Contains("messageType"))
                    {
                        GetSSEInfo sseInfo = JsonMapper.ToObject<GetSSEInfo>(fin);
                        if (string.IsNullOrEmpty(sseInfo.messageType))
                        {
                            TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = "该智能体有异常，请双击返回应用列表选择其他智能体" });
                        }
                        else
                        {
                            if (sseInfo.messageType == "KNOWLEDGE_END" || sseInfo.messageType == "KNOWLEDGE_START")
                            {
                                XMDebug.Log(this, $"PostRequestToStringAsycnTaskSuccuss result Is knowledge base");
                            }
                            else
                            {
                                SSEInfoQueue.Enqueue(sseInfo);
                                this.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Answer;
                            }
                        }
                    }
                    else
                    {
                        var errorData = JsonMapper.ToObject<ChatErrorData>(fin);
                        TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = $"{errorData.msg}" });
                        XMDebug.Log(this, $"PostRequestToStringAsycnTaskSuccuss error code {errorData.code} msg {errorData.msg}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                //TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = $"智能体数据解析异常 {ex.Message}" });
                XMDebug.LogError(this, $"PostRequestToStringAsycnTaskSuccuss error {ex.Message} info {info}");
            }
        }

        /// <summary>
        /// 智能体模型接收错误消息
        /// </summary>
        private void PostRequestToStringAsycnTaskFail(string code, string error)
        {
            TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = $"智能体模型数据错误：{error}" });
        }

        private void GetSTTVoice(GetSSEInfo sseInfo)
        {
            if (sseInfo.messageType == "CONTENT")
            {
                string content = $"{sseInfo.content}";
                content = Regex.Replace(content, @"\s+", "");
                string reasoningContent = $"{sseInfo.reasoningContent}";
                reasoningContent = Regex.Replace(reasoningContent, @"\s+", "");
                if (!string.IsNullOrEmpty(content))
                {
                    if (!isPlaySTT)
                    {
                        LoopPlayDelayContent();
                    }
                    ///累计需要延迟说话的文字
                    delayContent += content;
                }
                else if (!string.IsNullOrEmpty(reasoningContent))
                {
                    this.GetModel<ChatModel>().AIAgentTalkInfo.Value += $"{reasoningContent}";
                    XMDebug.Log(this, $"GetSTTVoice  reasoningContent {reasoningContent}");
                }
                else
                {
                    XMDebug.Log(this, $"GetSTTVoice content reasoningContent IS Empty");
                }
            }
            else if (sseInfo.messageType == "STREAM_END")
            {
                XMDebug.Log(this, $"GetSTTVoice STREAM_END");
                PlayFinallyDelayContent();
                TypeEventSystem.Global.Send(new AIAgentTextDoneEvent());
            }
        }

        /// <summary>
        /// 循环播放延迟数据
        /// </summary>
        private void LoopPlayDelayContent()
        {
            isPlaySTT = true;
            delayTimerId = TimerManager.Instance.AddTimeEvent(() => {
                if (!string.IsNullOrEmpty(delayContent))
                {
                    long time = DateTimeTool.CurrentTimeMilliseconds;
                    SendTTSInfo(delayContent, time);
                    AddSTTInfo(delayContent, time);
                    delayContent = "";
                }
            }, delayAddSEEContentTime, true, delayAddSEEContentTime);
        }

        /// <summary>
        /// 播放最后的延迟数据
        /// </summary>
        private void PlayFinallyDelayContent()
        {
            XMDebug.Log(this, $"FinallyDelayContent delayContent {delayContent}");
            if (!string.IsNullOrEmpty(delayContent))
            {
                long time = DateTimeTool.CurrentTimeMilliseconds;
                SendTTSInfo(delayContent, time);
                AddSTTInfo(delayContent, time);
                delayContent = "";
            }
        }

        /// <summary>
        /// 添加STT数据
        /// </summary>
        /// <param name="content"></param>
        private void AddSTTInfo(string content,long sttId)
        {
            TalkInfoData infoData = new TalkInfoData();
            infoData.talkContent = content;
            infoData.talkInfoTime = sttId;
            XMDebug.Log(this, $"AddSSTInfo sseid {sttId} infoData {content}");
            sseDicInfo.Add(sttId, infoData);
            talkInfoDataList.Add(sttId);
        }

        /// <summary>
        /// 发送TTS数据
        /// </summary>
        /// <param name="content"></param>
        private void SendTTSInfo(string content,long ttsId)
        {
            if (!this.GetModel<ChatModel>().IsReplyText.Value)
            {
                this.GetUtility<TTSUtility>().WSSpeak(content, ttsId, VoiceInfoEvent, VoiceInfoErrorEvent);
            }
        }

        private void VoiceInfoEvent(byte[] _clip, long sseId,bool isLoadSuc)
        {
            XMDebug.Log(this,$"VoiceInfoEvent sseid {sseId} isLoadSuc {isLoadSuc}");
            if (isLoadSuc)
            {
                if (CheckVoiceIsLoadDone(sseId))
                {
                    _ptw.isPcmLoadDone = true;
                }
            }
            else
            {
                _ptw.OnPCMData(_clip);
                _ptw.PlayPcm();
            }
        }

        private void VoiceInfoErrorEvent(string info)
        {
            XMDebug.LogError(this,$"VoiceInfoErrorEvent info {info}");
            TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = info });
        }

        /// <summary>
        /// 判断音频是否加载完毕
        /// </summary>
        private bool CheckVoiceIsLoadDone(long loadDoneId) 
        {
            sseDicInfo[loadDoneId].TalkDataLoadDone = true;
            bool isLoadDone = true;
            foreach (var item in sseDicInfo)
            {
                if (!item.Value.TalkDataLoadDone)
                    isLoadDone = false;
            }
            XMDebug.Log(this, $"CheckVoiceIsLoadDone {isLoadDone}");
            return isLoadDone;
        }
        #endregion

        #region 检测音频是否是唤醒词
        public void ChatCheckAudioIsWakeWord(AudioClip audio,string talkInfo) 
        {
#if NoWakeWord
            CheckAudioIsWakeWordAction(false, talkInfo);
#else
            this.GetUtility<WakeWordUtility>().CheckWakeWord(audio, talkInfo,CheckAudioIsWakeWordAction, CheckAudioWakeWordError);
#endif
        }

        private void CheckAudioIsWakeWordAction(bool isSuc,string talkInfo) 
        {
            XMDebug.Log(this, $"CheckAudioIsWakeWordAction isSuc {isSuc}");

            TypeEventSystem.Global.Send(new ChatCheckWakeWordResultEvent() { isWakeWord = isSuc,talkInfo = talkInfo});
        }
        private void CheckAudioWakeWordError(string errorInfo)
        {
            XMDebug.LogError(this, $"CheckAudioWakeWordError errorInfo {errorInfo}");
            TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = $"WakeWord {errorInfo}" });
        }

#endregion

        /// <summary>
        /// 用户说话
        /// </summary>
        public void UserTalkWakeWordAudioDone(bool isWakeWord,string talkInfo) 
        {
            AIAgentStateEnum aiAgentState = this.GetModel<ChatModel>().AIAgentState.Value;
            if (isWakeWord)
            {
                aiAgentStateDic[aiAgentState].ProcessAIAgentWakeWord();
            }
            else
            {
                aiAgentStateDic[aiAgentState].ProcessAIAgentNormalTalk(talkInfo);
            }
        }

        /// <summary>
        /// 智能体状态改变
        /// </summary>
        /// <param name="state"></param>
        public void AIAgentStateChange(AIAgentStateEnum state) 
        {
            _vad.SetVadInactivationIntervalSeconds(state == AIAgentStateEnum.Sleep ? 0.5f : 2.3f);
            aiAgentStateDic[state].EnterAIAgentState();
        }

        /// <summary>
        /// 停止AI智能体说话
        /// </summary>
        public void StopAIAgentTalk()
        {
            ClearDataInfo();
            _ptw.StopPCM();
            UIKit.GetPanel<AIChatUIChatPanel>().GetAIAgentTalkFrame().talkDone = true;
        }

        void PlayChatText(TalkInfoData data)
        {
            XMDebug.Log(this,$"PlayChatText talkContent {data.talkContent}");
            TalkInfoData infoData = data;
            this.GetModel<ChatModel>().AIAgentTalkInfo.Value = $"{this.GetModel<ChatModel>().AIAgentTalkInfo.Value}{infoData.talkContent}";
            this.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Answer;
        }

        public void ClearDataInfo()
        {
            this.GetUtility<TTSUtility>().WSClose(() =>
            {
                SSEInfoQueue.Clear();
            });
            sseDicInfo.Clear();
            SSEInfoQueue.Clear();
            talkInfoDataList.Clear();
            SSTQueue.Clear();
            delayContent = "";
            isPlaySTT = false;
            HttpRequestManager.Instance.StopPostRequestToStringAsycnTask();
            TimerManager.Instance.RemoveTimerEvent(delayTimerId);
        }
    }
}

