ManifestFileVersion: 0
CRC: 835380289
Hashes:
  AssetFileHash:
    serializedVersion: 2
    Hash: 1bfb9d3d46198ad56fcc3f773f1eaf78
  TypeTreeHash:
    serializedVersion: 2
    Hash: 4ae1ca063118395a0043f3e2f9c201d4
  IncrementalBuildHash:
    serializedVersion: 2
    Hash: 1bfb9d3d46198ad56fcc3f773f1eaf78
HashAppended: 0
ClassTypes:
- Class: 1
  Script: {instanceID: 0}
- Class: 21
  Script: {instanceID: 0}
- Class: 28
  Script: {instanceID: 0}
- Class: 48
  Script: {instanceID: 0}
- Class: 65
  Script: {instanceID: 0}
- Class: 114
  Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 75de07227afa5d941876d7cbeef64733, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 8a8695521f0d02e499659fee002a26c2, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 41c9bb1d861bcd94a8e7ac9f845d56f8, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: cfe4a0fe02f6f314a9a9f1037d2af021, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 32d8ed92e084f32468dd252482cd86f5, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 2a4db7a114972834c8e4117be1d82ba3, type: 3}
- Class: 115
  Script: {instanceID: 0}
- Class: 128
  Script: {instanceID: 0}
- Class: 213
  Script: {instanceID: 0}
- Class: 222
  Script: {instanceID: 0}
- Class: 224
  Script: {instanceID: 0}
- Class: 225
  Script: {instanceID: 0}
- Class: 687078895
  Script: {instanceID: 0}
SerializeReferenceClassIdentifiers:
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.Events.PersistentCallGroup
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.RectOffset
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.AnimationTriggers
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ColorBlock
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.FontData
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Navigation
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ScrollRect/ScrollRectEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Scrollbar/ScrollEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.SpriteState
Assets:
- Assets/chat-aihuman/Runtime/Prefab/UI/AIChat/AIChatUIAIAgentChoicePanel.prefab
Dependencies: []
