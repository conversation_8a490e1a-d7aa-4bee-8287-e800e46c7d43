using System;
using System.Collections;
using System.Collections.Generic;
using LitJson;
using System.Text;
using QFramework;
using UnityEngine;
using XM.Common;
using XM.Core;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

namespace XM.Core
{
    public class LoginController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IController
    {

#if UNITY_EDITOR
        [SerializeField]
        private string testLoginQRUrl = "https://allinone.uban360.com?token=";

        [SerializeField]
        private string testLoginQRToken = "44a6a204-ff89-4d9c-a796-63860235a1a7";
#endif

        void Start() 
        {
            this.SendCommand<LoginInitLoginSceneCommand>();
        }

        private void Update()
        {
            this.GetSystem<LoginSystem>().UpdateCamera();

#if UNITY_EDITOR
            if (Input.GetKeyDown(KeyCode.W))
            {
                this.GetSystem<LoginSystem>().TestLoginQRDecode(testLoginQRUrl,testLoginQRToken);
            }
#endif
        }
        void OnEnable() 
        {
            Register();
        }

        void OnDisable() 
        {
            UnRegister();
        }
        void Register()
        {
            TypeEventSystem.Global.Register<ClickRescannerButtonEvent>(OnClickRescannerButtonEvent);
            TypeEventSystem.Global.Register<ClickLoginScanErrorBackButtonEvent>(OnClickLoginScanErrorBackButtonEvent);
            TypeEventSystem.Global.Register<LoginClickARGlassApplicationItemEvent>(OnLoginClickARGlassApplicationItemEvent);
            TypeEventSystem.Global.Register<LoginARGlassClickAIAgentEvent>(OnLoginARGlassClickAIAgentEvent);
            TypeEventSystem.Global.Register<ClickLoginBackApplicationListEvent>(OnClickLoginBackApplicationListEvent);
            TypeEventSystem.Global.Register<ClickLoginReLoginConfirmButtonEvent>(OnClickLoginReLoginConfirmButtonEvent);
            TypeEventSystem.Global.Register<ClickLoginReLoginCancelButtonEvent>(OnClickLoginReLoginCancelButtonEvent);
            TypeEventSystem.Global.Register<LoginUserIdentityChoiceConfirmEvent>(OnLoginUserIdentityChoiceConfirmEvent);
            TypeEventSystem.Global.Register<LoginPPTChoiceConfimEvent>(OnLoginPPTChoiceConfimEvent);
            TypeEventSystem.Global.Register<LoginAutocueChoiceConfirmEvent>(OnLoginAutocueChoiceConfirmEvent);
            TypeEventSystem.Global.Register<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);
            TypeEventSystem.Global.Register<LoginResultEvent>(OnLoginResultEvent);

            InputManager.Instance.OnBack += OnBackEvent;
            InputManager.Instance.OnTripleTap += OnOnTripleTapEvent;
        }


        void UnRegister()
        {
            TypeEventSystem.Global.UnRegister<ClickRescannerButtonEvent>(OnClickRescannerButtonEvent);
            TypeEventSystem.Global.UnRegister<ClickLoginScanErrorBackButtonEvent>(OnClickLoginScanErrorBackButtonEvent);
            TypeEventSystem.Global.UnRegister<LoginClickARGlassApplicationItemEvent>(OnLoginClickARGlassApplicationItemEvent);
            TypeEventSystem.Global.UnRegister<LoginARGlassClickAIAgentEvent>(OnLoginARGlassClickAIAgentEvent);
            TypeEventSystem.Global.UnRegister<ClickLoginBackApplicationListEvent>(OnClickLoginBackApplicationListEvent);
            TypeEventSystem.Global.UnRegister<ClickLoginReLoginConfirmButtonEvent>(OnClickLoginReLoginConfirmButtonEvent);
            TypeEventSystem.Global.UnRegister<ClickLoginReLoginCancelButtonEvent>(OnClickLoginReLoginCancelButtonEvent);
            TypeEventSystem.Global.UnRegister<LoginUserIdentityChoiceConfirmEvent>(OnLoginUserIdentityChoiceConfirmEvent);
            TypeEventSystem.Global.UnRegister<LoginPPTChoiceConfimEvent>(OnLoginPPTChoiceConfimEvent);
            TypeEventSystem.Global.UnRegister<LoginAutocueChoiceConfirmEvent>(OnLoginAutocueChoiceConfirmEvent);
            TypeEventSystem.Global.UnRegister<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);
            TypeEventSystem.Global.UnRegister<LoginResultEvent>(OnLoginResultEvent);

            if (InputManager.Instance != null)
            {
                InputManager.Instance.OnBack -= OnBackEvent;
                InputManager.Instance.OnTripleTap -= OnOnTripleTapEvent;
            }
        }


        private void OnClickRescannerButtonEvent(ClickRescannerButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickRescannerButtonEvent");
            UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Scan);
            this.SendCommand<LoginAutoLoginCommand>();
        }
        private void OnClickLoginScanErrorBackButtonEvent(ClickLoginScanErrorBackButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickLoginScanErrorBackButtonEvent");
            UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Scan);
            this.SendCommand<LoginAutoLoginCommand>();
        }

        private void OnLoginClickARGlassApplicationItemEvent(LoginClickARGlassApplicationItemEvent @event)
        {
            var appData = @event.itemData;
            XMDebug.Log(this, $"OnLoginClickARGlassApplicationItemEvent appType {appData.appType}");
            AbstractCommand command = null;
            switch (appData.appType)
            {
                case AppType.AIChat:
                    LoginAppAIChatData chatData = appData as LoginAppAIChatData;
                    command = new LoginEnterAIChatApplicationCommand(chatData.itemData);
                    break;
                case AppType.LocalPPT:
                    LoginAppSystemData pptData = appData as LoginAppSystemData;
                    command = new LoginEnterAIChatApplicationCommand(pptData.itemData);
                    //command = new LoginEnterPPTApplicationCommand();
                    break;
                case AppType.Autocue:
                    LoginAppSystemData autoData = appData as LoginAppSystemData;
                    command = new LoginEnterAIChatApplicationCommand(autoData.itemData);
                    break;
                default:
                    break;
            }
            this.SendCommand(command);
        }

        //{"code":10003,"msg":"智能体指令信息缺失,请先保存智能体","num":0}
        private void OnLoginARGlassClickAIAgentEvent(LoginARGlassClickAIAgentEvent @event)
        {
            XMDebug.Log(this, $"OnLoginARGlassClickAIAgentEvent");
            LoginEnterAIAgentCommand command = new LoginEnterAIAgentCommand(@event.aiAgent);
            this.SendCommand(command);
        }

        private void OnClickLoginBackApplicationListEvent(ClickLoginBackApplicationListEvent @event)
        {
            XMDebug.Log(this, $"OnClickLoginBackApplicationListEvent");
            this.SendCommand<LoginRefreshEnterApplicationListPageCommand>();
        }

        private void OnClickLoginReLoginConfirmButtonEvent(ClickLoginReLoginConfirmButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickLoginReLoginConfirmButtonEvent");
            this.GetSystem<LoginSystem>().Logout();
            this.SendCommand<LoginAutoLoginCommand>();
        }
        private void OnClickLoginReLoginCancelButtonEvent(ClickLoginReLoginCancelButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickLoginReLoginCancelButtonEvent");
            this.SendCommand<LoginRefreshEnterApplicationListPageCommand>();
        }
        private void OnLoginUserIdentityChoiceConfirmEvent(LoginUserIdentityChoiceConfirmEvent @event)
        {
            XMDebug.Log(this, $"OnLoginUserIdentityChoiceConfirmEvent {@event.itemData.orgCode}");
            this.GetModel<UserModel>().CurrentSelectUserIdentityOrgCode = @event.itemData.orgCode;
            this.SendCommand<LoginRefreshEnterApplicationListPageCommand>();
        }

        private void OnLoginPPTChoiceConfimEvent(LoginPPTChoiceConfimEvent @event)
        {
            XMDebug.Log(this, $"OnLoginPPTChoiceConfimEvent {@event.itemInfo.title} path {@event.itemInfo.viewFileUrl}");
            this.SendCommand(new LoginEnterPPTSceneCommand(@event.itemInfo));
        }

        private void OnLoginAutocueChoiceConfirmEvent(LoginAutocueChoiceConfirmEvent @event)
        {
            XMDebug.Log(this, $"OnLoginAutocueChoiceConfirmEvent {@event.itemInfo.documentName} id {@event.itemInfo.id}");
            this.SendCommand(new LoginEnterAutocueSceneCommand(@event.itemInfo));
        }

        private void OnDoubleTapRayneoEvent(DoubleTapRayneoEvent @event)
        {
            XMDebug.Log(this, $"OnDoubleTapRayneoEvent");
            this.SendCommand<LoginBackPageCommand>();
        }
        private void OnOnTripleTapEvent()
        {
            XMDebug.Log(this, $"OnOnTripleTapEvent");
            UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.IdentityChoice);
        }

        private void OnLoginResultEvent(LoginResultEvent @event)
        {
            XMDebug.Log(this, $"OnLoginResultEvent IsSuccess {@event.isSuc}");
            if (@event.isSuc)
            {
                this.SendCommand<LoginRefreshEnterCurrentPageCommand>();
            }
            else
            {
                TypeEventSystem.Global.Send(new LoginFailInfoEvent() { errorInfo = @event.info });
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Fail);
            }
        }

        private void OnBackEvent()
        {
            XMDebug.Log(this, $"OnBackEvent");
            this.SendCommand<LoginBackPageCommand>();
        }


        public IArchitecture GetArchitecture()
        {
            return AIArchitecture.Interface;
        }
    }
}
