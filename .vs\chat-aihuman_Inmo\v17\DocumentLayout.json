{"Version": 1, "WorkspaceRootPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ppt\\controller\\pptcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ppt\\controller\\pptcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\data\\logindata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\data\\logindata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\chat\\event\\chatevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\chat\\event\\chatevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A914E89F-FCC5-44DC-25B3-EACD540FCB7F}|QFramework.csproj|e:\\unitypro\\aihuman\\chat-aihuman_inmo\\assets\\chat-aihuman\\runtime\\scripts\\common\\framework\\qframework\\framework\\scripts\\qframework.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A914E89F-FCC5-44DC-25B3-EACD540FCB7F}|QFramework.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\common\\framework\\qframework\\framework\\scripts\\qframework.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\login\\controller\\logincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\login\\controller\\logincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|e:\\unitypro\\aihuman\\chat-aihuman_inmo\\assets\\chat-aihuman\\runtime\\scripts\\common\\unitymainthreaddispatcher.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\common\\unitymainthreaddispatcher.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|e:\\unitypro\\aihuman\\chat-aihuman_inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\utility\\localapputility.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\utility\\localapputility.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ppt\\pptprepare.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ppt\\pptprepare.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\aichatuichatpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\aichatuichatpanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\talkframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\talkframe.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\aichatuiaiagentchoicepanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\aichatui\\aichatuiaiagentchoicepanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\loginui\\loginuiitem_aiagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{65063EA2-0356-F6FF-6792-3449211CC843}|Assembly-CSharp.csproj|solutionrelative:assets\\chat-aihuman\\runtime\\scripts\\core\\ui\\loginui\\loginuiitem_aiagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 412, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "QFramework.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Framework\\Scripts\\QFramework.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Framework\\Scripts\\QFramework.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Framework\\Scripts\\QFramework.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Framework\\Scripts\\QFramework.cs", "ViewState": "AgIAAOMAAAAAAAAAAAD4v4EAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:35:36.277Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UnityMainThreadDispatcher.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\UnityMainThreadDispatcher.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\UnityMainThreadDispatcher.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\UnityMainThreadDispatcher.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\UnityMainThreadDispatcher.cs", "ViewState": "AgIAACEAAAAAAAAAAAASwDAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T07:43:30.205Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "LocalAppUtility.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Utility\\LocalAppUtility.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Utility\\LocalAppUtility.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Utility\\LocalAppUtility.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Utility\\LocalAppUtility.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAwwAoAAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:55:34.559Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "PPTPrepare.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\PPTPrepare.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\PPTPrepare.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\PPTPrepare.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\PPTPrepare.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvxkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:55:07.884Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ChatEvent.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Chat\\Event\\ChatEvent.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Chat\\Event\\ChatEvent.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Chat\\Event\\ChatEvent.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Chat\\Event\\ChatEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:52:51.703Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LoginData.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Data\\LoginData.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Data\\LoginData.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Data\\LoginData.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Data\\LoginData.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAUwAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:51:58.991Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "PPTController.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\Controller\\PPTController.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\Controller\\PPTController.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\Controller\\PPTController.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\PPT\\Controller\\PPTController.cs", "ViewState": "AgIAABAAAAAAAAAAAAAIwBcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:03:23.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "AIChatUIChatPanel.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIChatPanel.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIChatPanel.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIChatPanel.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIChatPanel.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAHgAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:03:35.506Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "TalkFrame.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\TalkFrame.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\TalkFrame.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\TalkFrame.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\TalkFrame.cs", "ViewState": "AgIAABUAAAAAAAAAAAAswBsAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:03:34.362Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "AIChatUIAIAgentChoicePanel.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIAIAgentChoicePanel.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIAIAgentChoicePanel.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIAIAgentChoicePanel.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\AIChatUI\\AIChatUIAIAgentChoicePanel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:01:02.995Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "LoginUIItem_AIAgent.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\LoginUI\\LoginUIItem_AIAgent.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\LoginUI\\LoginUIItem_AIAgent.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\LoginUI\\LoginUIItem_AIAgent.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\UI\\LoginUI\\LoginUIItem_AIAgent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:58:57.327Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "LoginController.cs", "DocumentMoniker": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Login\\Controller\\LoginController.cs", "RelativeDocumentMoniker": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Login\\Controller\\LoginController.cs", "ToolTip": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Login\\Controller\\LoginController.cs", "RelativeToolTip": "Assets\\chat-aihuman\\Runtime\\Scripts\\Core\\Login\\Controller\\LoginController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:56:46.545Z", "EditorCaption": ""}]}]}]}