Shader "XM/Standard/MobileMirrorReflection"
{
    Properties
    {
        [Header(Base Settings)]
        _MainTex ("Base Texture", 2D) = "white" {}
        _Color ("Base Color", Color) = (1,1,1,1)
        _Metallic ("Metallic", Range(0,1)) = 0.8
        _Glossiness ("Smoothness", Range(0,1)) = 0.9
        
        [Header(Reflection Settings)]
        _ReflectionTex ("Reflection Texture", 2D) = "black" {}
        _ReflectionIntensity ("Reflection Intensity", Range(0,2)) = 1.0
        _FresnelPower ("Fresnel Power", Range(0.1,5)) = 1.0
        _FresnelBias ("Fresnel Bias", Range(0,1)) = 0.0
        
        [Header(Normal Settings)]
        _BumpMap ("Normal Map", 2D) = "bump" {}
        _BumpScale ("Normal Scale", Range(0,2)) = 1.0
        
        [Header(Distortion Settings)]
        _DistortionStrength ("Distortion Strength", Range(0,0.1)) = 0.01
        
        [Header(Mobile Optimization)]
        [Toggle] _EnableReflection ("Enable Reflection", Float) = 1
        [Toggle] _EnableNormalMap ("Enable Normal Map", Float) = 1
        _LODLevel ("LOD Level", Range(0,3)) = 0
        
        [Header(Standard Pipeline)]
        _EmissionColor ("Emission Color", Color) = (0,0,0,1)
        _EmissionMap ("Emission Map", 2D) = "white" {}
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType"="Opaque" 
            "Queue"="Geometry"
        }
        
        LOD 300
        
        CGPROGRAM
        #pragma surface surf StandardMirror fullforwardshadows
        #pragma target 3.0
        
        // Mobile optimization pragmas
        #pragma shader_feature _ENABLEREFLECTION_ON
        #pragma shader_feature _ENABLENORMALMAP_ON
        #pragma multi_compile_fog
        
        sampler2D _MainTex;
        sampler2D _ReflectionTex;
        sampler2D _BumpMap;
        sampler2D _EmissionMap;
        
        struct Input
        {
            float2 uv_MainTex;
            float2 uv_BumpMap;
            float3 worldPos;
            float3 worldNormal;
            float3 viewDir;
            float4 screenPos;
            INTERNAL_DATA
        };
        
        fixed4 _Color;
        half _Metallic;
        half _Glossiness;
        half _ReflectionIntensity;
        half _FresnelPower;
        half _FresnelBias;
        half _BumpScale;
        half _DistortionStrength;
        half _LODLevel;
        fixed4 _EmissionColor;
        
        // Custom lighting model for mirror reflection
        half4 LightingStandardMirror(SurfaceOutputStandard s, half3 viewDir, UnityGI gi)
        {
            // Standard lighting calculation
            half4 c = LightingStandard(s, viewDir, gi);
            return c;
        }
        
        void LightingStandardMirror_GI(SurfaceOutputStandard s, UnityGIInput data, inout UnityGI gi)
        {
            LightingStandard_GI(s, data, gi);
        }
        
        float3 GetReflectionColor(Input IN, float3 worldNormal)
        {
            #ifdef _ENABLEREFLECTION_ON
            // Calculate reflection vector
            float3 worldViewDir = normalize(UnityWorldSpaceViewDir(IN.worldPos));
            float3 worldRefl = reflect(-worldViewDir, worldNormal);
            
            // Screen space UV calculation
            float2 screenUV = IN.screenPos.xy / IN.screenPos.w;
            
            // Add distortion effect
            float2 distortion = worldNormal.xy * _DistortionStrength;
            screenUV += distortion;
            
            // Sample reflection texture with LOD
            return tex2Dlod(_ReflectionTex, float4(screenUV, 0, _LODLevel)).rgb;
            #else
            return float3(0, 0, 0);
            #endif
        }
        
        float CalculateFresnel(float3 viewDir, float3 normal)
        {
            float NdotV = saturate(dot(normal, viewDir));
            return _FresnelBias + (1.0 - _FresnelBias) * pow(1.0 - NdotV, _FresnelPower);
        }
        
        void surf(Input IN, inout SurfaceOutputStandard o)
        {
            // Base texture sampling
            fixed4 c = tex2D(_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = c.rgb;
            
            // Normal mapping
            #ifdef _ENABLENORMALMAP_ON
            o.Normal = UnpackScaleNormal(tex2D(_BumpMap, IN.uv_BumpMap), _BumpScale);
            #else
            o.Normal = float3(0, 0, 1);
            #endif
            
            // Get world normal for reflection calculation
            float3 worldNormal = WorldNormalVector(IN, o.Normal);
            
            // Reflection calculation
            float3 reflectionColor = GetReflectionColor(IN, worldNormal);
            
            // Fresnel calculation
            float fresnel = CalculateFresnel(IN.viewDir, o.Normal);
            
            // Apply reflection to albedo
            o.Albedo = lerp(o.Albedo, reflectionColor, fresnel * _ReflectionIntensity * _Glossiness);
            
            // Metallic workflow
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            o.Alpha = c.a;
            
            // Emission
            o.Emission = tex2D(_EmissionMap, IN.uv_MainTex).rgb * _EmissionColor.rgb;
        }
        ENDCG
    }
    
    // Mobile optimized version
    SubShader
    {
        Tags 
        { 
            "RenderType"="Opaque" 
            "Queue"="Geometry"
        }
        
        LOD 150
        
        CGPROGRAM
        #pragma surface surf Lambert
        #pragma target 2.0
        
        sampler2D _MainTex;
        sampler2D _ReflectionTex;
        
        struct Input
        {
            float2 uv_MainTex;
            float3 worldPos;
            float3 worldNormal;
            float3 viewDir;
        };
        
        fixed4 _Color;
        half _Glossiness;
        half _ReflectionIntensity;
        
        void surf(Input IN, inout SurfaceOutput o)
        {
            fixed4 c = tex2D(_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = c.rgb;
            
            // Simplified reflection effect
            float fresnel = 1.0 - saturate(dot(normalize(IN.viewDir), IN.worldNormal));
            fresnel = pow(fresnel, 2.0);
            
            // Use skybox as reflection
            float3 worldViewDir = normalize(UnityWorldSpaceViewDir(IN.worldPos));
            float3 worldRefl = reflect(-worldViewDir, IN.worldNormal);
            float3 reflectionColor = UNITY_SAMPLE_TEXCUBE(unity_SpecCube0, worldRefl).rgb;
            
            o.Albedo = lerp(o.Albedo, reflectionColor, fresnel * _ReflectionIntensity * _Glossiness * 0.5);
            o.Alpha = c.a;
        }
        ENDCG
    }
    
    FallBack "Legacy Shaders/Reflective/Diffuse"
}
