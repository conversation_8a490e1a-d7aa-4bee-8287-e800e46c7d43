#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using XM.Core;

namespace XM.Editor
{
    /// <summary>
    /// 标准管线镜面反射编辑器
    /// </summary>
    [CustomEditor(typeof(StandardMirrorReflection))]
    public class StandardMirrorReflectionEditor : UnityEditor.Editor
    {
        private StandardMirrorReflection standardMirrorReflection;
        
        private SerializedProperty reflectionLayers;
        private SerializedProperty disablePixelLights;
        private SerializedProperty textureSize;
        private SerializedProperty clipPlaneOffset;
        private SerializedProperty useOcclusionCulling;
        private SerializedProperty reflectionDistance;
        private SerializedProperty maxReflectionFPS;
        private SerializedProperty enableMobileOptimization;
        private SerializedProperty quality;
        private SerializedProperty enableFog;
        private SerializedProperty enableSkybox;
        private SerializedProperty renderingPath;
        
        private void OnEnable()
        {
            standardMirrorReflection = (StandardMirrorReflection)target;
            
            reflectionLayers = serializedObject.FindProperty("reflectionLayers");
            disablePixelLights = serializedObject.FindProperty("disablePixelLights");
            textureSize = serializedObject.FindProperty("textureSize");
            clipPlaneOffset = serializedObject.FindProperty("clipPlaneOffset");
            useOcclusionCulling = serializedObject.FindProperty("useOcclusionCulling");
            reflectionDistance = serializedObject.FindProperty("reflectionDistance");
            maxReflectionFPS = serializedObject.FindProperty("maxReflectionFPS");
            enableMobileOptimization = serializedObject.FindProperty("enableMobileOptimization");
            quality = serializedObject.FindProperty("quality");
            enableFog = serializedObject.FindProperty("enableFog");
            enableSkybox = serializedObject.FindProperty("enableSkybox");
            renderingPath = serializedObject.FindProperty("renderingPath");
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("标准管线镜面反射设置", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 反射设置
            EditorGUILayout.LabelField("反射设置", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(reflectionLayers, new GUIContent("反射层级"));
            EditorGUILayout.PropertyField(disablePixelLights, new GUIContent("禁用像素光源"));
            EditorGUILayout.IntSlider(textureSize, 64, 2048, new GUIContent("纹理大小"));
            EditorGUILayout.Slider(clipPlaneOffset, 0f, 1f, new GUIContent("裁剪平面偏移"));
            
            EditorGUILayout.Space();
            
            // 性能优化
            EditorGUILayout.LabelField("性能优化", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(enableMobileOptimization, new GUIContent("启用移动端优化"));
            EditorGUILayout.PropertyField(quality, new GUIContent("反射质量"));
            EditorGUILayout.PropertyField(useOcclusionCulling, new GUIContent("使用遮挡剔除"));
            EditorGUILayout.PropertyField(reflectionDistance, new GUIContent("反射距离"));
            EditorGUILayout.IntSlider(maxReflectionFPS, 1, 60, new GUIContent("最大反射FPS"));
            
            EditorGUILayout.Space();
            
            // 标准管线特定设置
            EditorGUILayout.LabelField("标准管线设置", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(enableFog, new GUIContent("启用雾效"));
            EditorGUILayout.PropertyField(enableSkybox, new GUIContent("启用天空盒"));
            EditorGUILayout.PropertyField(renderingPath, new GUIContent("渲染路径"));
            
            EditorGUILayout.Space();
            
            // 设备信息
            DrawDeviceInfo();
            
            EditorGUILayout.Space();
            
            // 快速设置按钮
            DrawQuickSetupButtons();
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private void DrawDeviceInfo()
        {
            EditorGUILayout.LabelField("设备信息", EditorStyles.boldLabel);
            
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.TextField("GPU", SystemInfo.graphicsDeviceName);
            EditorGUILayout.TextField("内存", $"{SystemInfo.systemMemorySize} MB");
            EditorGUILayout.TextField("GPU内存", $"{SystemInfo.graphicsMemorySize} MB");
            EditorGUILayout.TextField("渲染管线", "标准管线 (Built-in)");
            EditorGUI.EndDisabledGroup();
            
            // 推荐设置
            string recommendation = GetRecommendedSettings();
            if (!string.IsNullOrEmpty(recommendation))
            {
                EditorGUILayout.HelpBox(recommendation, MessageType.Info);
            }
        }
        
        private string GetRecommendedSettings()
        {
            int memorySize = SystemInfo.systemMemorySize;
            
            if (memorySize < 2000)
            {
                return "检测到低端设备，建议使用低质量设置，启用移动端优化，使用VertexLit渲染路径";
            }
            else if (memorySize < 4000)
            {
                return "检测到中端设备，建议使用中等质量设置，禁用像素光源";
            }
            else if (memorySize >= 6000)
            {
                return "检测到高端设备，可以使用高质量设置，启用所有特效";
            }
            
            return "";
        }
        
        private void DrawQuickSetupButtons()
        {
            EditorGUILayout.LabelField("快速设置", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("低端设备设置"))
            {
                ApplyLowEndSettings();
            }
            
            if (GUILayout.Button("中端设备设置"))
            {
                ApplyMidEndSettings();
            }
            
            if (GUILayout.Button("高端设备设置"))
            {
                ApplyHighEndSettings();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("创建标准管线材质"))
            {
                CreateStandardMirrorMaterial();
            }
            
            if (GUILayout.Button("应用到选中对象"))
            {
                ApplyToSelectedObjects();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void ApplyLowEndSettings()
        {
            enableMobileOptimization.boolValue = true;
            quality.enumValueIndex = (int)StandardMirrorReflection.ReflectionQuality.Low;
            disablePixelLights.boolValue = true;
            maxReflectionFPS.intValue = 15;
            reflectionDistance.floatValue = 30f;
            renderingPath.enumValueIndex = (int)RenderingPath.VertexLit;
            enableFog.boolValue = false;
            useOcclusionCulling.boolValue = true;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void ApplyMidEndSettings()
        {
            enableMobileOptimization.boolValue = true;
            quality.enumValueIndex = (int)StandardMirrorReflection.ReflectionQuality.Medium;
            disablePixelLights.boolValue = true;
            maxReflectionFPS.intValue = 20;
            reflectionDistance.floatValue = 50f;
            renderingPath.enumValueIndex = (int)RenderingPath.Forward;
            enableFog.boolValue = true;
            useOcclusionCulling.boolValue = true;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void ApplyHighEndSettings()
        {
            enableMobileOptimization.boolValue = false;
            quality.enumValueIndex = (int)StandardMirrorReflection.ReflectionQuality.High;
            disablePixelLights.boolValue = false;
            maxReflectionFPS.intValue = 30;
            reflectionDistance.floatValue = 100f;
            renderingPath.enumValueIndex = (int)RenderingPath.Forward;
            enableFog.boolValue = true;
            enableSkybox.boolValue = true;
            useOcclusionCulling.boolValue = true;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void CreateStandardMirrorMaterial()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建标准管线镜面材质",
                "StandardMirrorMaterial",
                "mat",
                "选择保存位置");
                
            if (!string.IsNullOrEmpty(path))
            {
                Shader mirrorShader = Shader.Find("XM/Standard/MobileMirrorReflection");
                if (mirrorShader != null)
                {
                    Material material = new Material(mirrorShader);
                    AssetDatabase.CreateAsset(material, path);
                    AssetDatabase.SaveAssets();
                    
                    EditorGUIUtility.PingObject(material);
                    Selection.activeObject = material;
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", "找不到标准管线镜面反射Shader: XM/Standard/MobileMirrorReflection", "确定");
                }
            }
        }
        
        private void ApplyToSelectedObjects()
        {
            GameObject[] selectedObjects = Selection.gameObjects;
            
            foreach (GameObject obj in selectedObjects)
            {
                if (obj.GetComponent<StandardMirrorReflection>() == null)
                {
                    obj.AddComponent<StandardMirrorReflection>();
                }
            }
            
            EditorUtility.DisplayDialog("完成", $"已为 {selectedObjects.Length} 个对象添加标准管线镜面反射组件", "确定");
        }
    }
    
    /// <summary>
    /// 标准管线镜面反射工具菜单
    /// </summary>
    public class StandardMirrorReflectionTools
    {
        [MenuItem("XM Tools/Mirror Reflection/Add Standard Mirror Reflection")]
        public static void AddStandardMirrorReflection()
        {
            GameObject[] selectedObjects = Selection.gameObjects;
            
            if (selectedObjects.Length == 0)
            {
                EditorUtility.DisplayDialog("提示", "请先选择要添加镜面反射的对象", "确定");
                return;
            }
            
            foreach (GameObject obj in selectedObjects)
            {
                if (obj.GetComponent<StandardMirrorReflection>() == null)
                {
                    obj.AddComponent<StandardMirrorReflection>();
                }
            }
            
            EditorUtility.DisplayDialog("完成", $"已为 {selectedObjects.Length} 个对象添加标准管线镜面反射组件", "确定");
        }
        
        [MenuItem("XM Tools/Mirror Reflection/Create Standard Mirror Material")]
        public static void CreateStandardMirrorMaterial()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建标准管线镜面材质",
                "StandardMirrorMaterial",
                "mat",
                "选择保存位置");
                
            if (!string.IsNullOrEmpty(path))
            {
                Shader mirrorShader = Shader.Find("XM/Standard/MobileMirrorReflection");
                if (mirrorShader != null)
                {
                    Material material = new Material(mirrorShader);
                    AssetDatabase.CreateAsset(material, path);
                    AssetDatabase.SaveAssets();
                    
                    EditorGUIUtility.PingObject(material);
                    Selection.activeObject = material;
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", "找不到标准管线镜面反射Shader: XM/Standard/MobileMirrorReflection", "确定");
                }
            }
        }
    }
}
#endif
