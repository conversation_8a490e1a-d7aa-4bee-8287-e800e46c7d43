using QFramework;
using System;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using XM.Common;


public class AudioPlayDoneEvent 
{
    
}



/// <summary>
/// Wave Hander information
/// </summary>
public struct HeaderType
{
    public byte[] riff;                 /*RIFF类资源文件头部 4byte*/
    public uint file_len;                /*文件长度4byte*/
    public byte[] wave;                  /*"WAVE"标志4byte*/
    public byte[] fmt;                   /*"fmt"标志4byte*/
    public uint NI1;                     /*过渡字节4byte*/
    public ushort format_type;           /*格式类别(10H为PCM形式的声音数据)2byte*/
    public ushort Channels;              /*Channels 1 = 单声道; 2 = 立体声2byte*/
    public uint frequency;               /*采样频率4byte*/
    public uint trans_speed;             /*音频数据传送速率4byte*/
    public ushort dataBlock;             /*数据块的调整数（按字节算的）2byte*/
    public ushort sample_bits;           /*样本的数据位数(8/16) 2byte*/
    public byte[] data;                  /*数据标记符"data" 4byte*/
    public uint wav_len;                 /*语音数据的长度 4byte*/
}

public class PCMToWAV : MonoBehaviour
{
    /// <summary>
    /// ERROR MESSAGE
    /// </summary>
    const string ERRFILENOTEXITS = "File is Not Exits.";
    const string ERRFILEISNOTWAVE = "File is not Wava.";

    /// <summary>
    /// 当前音频帧率
    /// </summary>
    private float audioFPS = 25f;

    /// <summary>
    /// 当前音频时间
    /// </summary>
    private float curAudioTime = 0;

    /// <summary>
    /// 音频播放文件
    /// </summary>
    public AudioSource audioS;


    /// <summary>
    /// PCM的播放Clip
    /// </summary>
    AudioClip clip;
    /// <summary>
    /// 写入音频位置
    /// </summary>
    int writeAudioPos = 0;


    /// <summary>
    /// 上次打断的时间
    /// </summary>
    public DateTime lastStopTimeStamp;

    public bool isPcmLoadDone = false;



    private HeaderType wavHander;       //定义一个头结构体

    private void Start()
    {
        InitialStruct();


        lastStopTimeStamp = new DateTime(1992,05,08);
    }


    public void OnPCMData(byte[] pcmData)
    {
        try
        {
            if (clip == null)
            {
                ///创建长度为100秒的音频【80000*20 = 100秒】
                clip = AudioClip.Create("pcmData", (80000 * 20) * 3, 1, 24000, false);
            }

            byte[] pcmBytes = pcmData;

            byte[] sliceArray = new byte[44];
            sliceArray = sliceArray.Concat(pcmBytes).ToArray();
            sliceArray = InitChildHeader(sliceArray);
            float[] arr = ToAudio.ClipFloatArray(sliceArray);
            clip.SetData(arr, writeAudioPos);
            writeAudioPos += arr.Length;
            curAudioTime += pcmBytes.Length / (24000 * (16f / 8f));
            XMDebug.Log(this, $"-> OnPCMData:writeAudioPos:{writeAudioPos} curAudioTime {curAudioTime}");
        }
        catch (Exception se)
        {
            XMDebug.LogError(this, $"->writeAudioPos:{se.Message}" );
        }
    }


    public void TestCreateClip(byte[] pcmD)
    {
        if (clip == null)
        {
            ///创建长度为100秒的音频
            clip = AudioClip.Create("pcmData", 80000 * 20, 1, 24000, false);
        }

        byte[] pcmBytes = pcmD;

        byte[] sliceArray = new byte[44];
        sliceArray = sliceArray.Concat(pcmBytes).ToArray();
        sliceArray = InitChildHeader(sliceArray);
        clip = ToAudio.Clip(sliceArray, 24000);
        //clip.SetData(arr, writeAudioPos);
        //writeAudioPos += arr.Length;
    }

    private void Update()
    {
        if (audioS.clip != null)
        {
            //Debug.Log($"audio.time {audioS.time} curAudioTime {curAudioTime}");
            if (isPcmLoadDone && audioS.time >= curAudioTime + 0.2f)
            {
                StopPCM();
                TypeEventSystem.Global.Send(new AIAgentTalkDoneEvent());
            }
        }
    }
    /// <summary>
    /// 初始化结构体中的数组长度，分配内存
    /// </summary>
    private void InitialStruct()
    {
        wavHander.riff = new byte[4];//RIFF
        wavHander.wave = new byte[4];//WAVE
        wavHander.fmt = new byte[4];//fmt 
        wavHander.data = new byte[4];//data
    }

    /// <summary>
    /// 为PCM文件构建文件头，准备转换为WAV文件
    /// </summary>
    /// <returns>构建成功返回真</returns>
    private byte[] InitChildHeader(byte[] pcmData)
    {
        wavHander.riff = Encoding.ASCII.GetBytes("RIFF");   /*RIFF类资源文件头部 4byte*/
        wavHander.file_len = (uint)(pcmData.Length);              /*文件长度4byte*/
        wavHander.wave = Encoding.ASCII.GetBytes("WAVE");     /*"WAVE"标志4byte*/
        wavHander.fmt = Encoding.ASCII.GetBytes("fmt ");      /*"fmt"标志4byte*/
        wavHander.NI1 = 0x10;                               /*过渡字节4byte*/
        wavHander.format_type = 0x01;                       /*格式类别(10H为PCM形式的声音数据)2byte*/
        wavHander.Channels = 1;                          /*Channels 1 = 单声道; 2 = 立体声2byte*/
        wavHander.frequency = 0x3E80;                       /*采样频率4byte*/
        wavHander.trans_speed = 0x3E80;                     /*音频数据传送速率4byte*/
        wavHander.dataBlock = 0x02;                         /*数据块的调整数（按字节算的）2byte*/
        wavHander.sample_bits = 0x10;                       /*样本的数据位数(8/16) 2byte*/
        wavHander.data = Encoding.ASCII.GetBytes("data");   /*数据标记符"data" 4byte*/
        wavHander.wav_len = (uint)(pcmData.Length - 44);                /*语音数据的长度 4byte*/
        byte[] byt2;//临时变量 ，保存2位的整数
        byte[] byt4;//临时变量， 保存4位的整数
        Encoding.ASCII.GetBytes(Encoding.ASCII.GetString(wavHander.riff), 0, 4, pcmData, 0);/*RIFF类资源文件头部 4byte*/
        byt4 = BitConverter.GetBytes(wavHander.file_len); /*文件长度4byte*/
        Array.Copy(byt4, 0, pcmData, 4, 4);
        Encoding.ASCII.GetBytes(Encoding.ASCII.GetString(wavHander.wave), 0, 4, pcmData, 8);/*"WAVE"标志4byte*/
        Encoding.ASCII.GetBytes(Encoding.ASCII.GetString(wavHander.fmt), 0, 4, pcmData, 12);/*"fmt"标志4byte*/
        byt4 = BitConverter.GetBytes(wavHander.NI1);/*过渡字节4byte*/
        Array.Copy(byt4, 0, pcmData, 16, 4);
        byt2 = BitConverter.GetBytes(wavHander.format_type);/*格式类别(10H为PCM形式的声音数据)2byte*/
        Array.Copy(byt2, 0, pcmData, 20, 2);
        byt2 = BitConverter.GetBytes(wavHander.Channels);/*Channels 1 = 单声道; 2 = 立体声2byte*/
        Array.Copy(byt2, 0, pcmData, 22, 2);
        byt4 = BitConverter.GetBytes(wavHander.frequency);/*采样频率4byte*/
        Array.Copy(byt4, 0, pcmData, 24, 4);
        byt4 = BitConverter.GetBytes(wavHander.trans_speed);/*音频数据传送速率4byte*/
        Array.Copy(byt4, 0, pcmData, 28, 4);
        byt2 = BitConverter.GetBytes(wavHander.dataBlock);/*数据块的调整数（按字节算的）2byte*/
        Array.Copy(byt2, 0, pcmData, 32, 2);
        byt2 = BitConverter.GetBytes(wavHander.sample_bits);/*样本的数据位数(8/16) 2byte*/
        Array.Copy(byt2, 0, pcmData, 34, 2);
        Encoding.ASCII.GetBytes(Encoding.ASCII.GetString(wavHander.data), 0, 4, pcmData, 36);/*数据标记符"data" 4byte*/
        byt4 = BitConverter.GetBytes(wavHander.wav_len); /*语音数据的长度 4byte*/
        Array.Copy(byt4, 0, pcmData, 40, 4);
        return pcmData;
    }
    /// <summary>
    /// 写文件操作
    /// </summary>
    /// <param name="filename">文件路径</param>
    /// <param name="pbuff">文件数据</param>
    private void WriteFile(string filename, byte[] pbuff)
    {
        if (File.Exists(filename) == true)
            File.Delete(filename);
        FileStream sw = File.OpenWrite(filename);
        if (pbuff != null && sw != null)
        {
            sw.Write(pbuff, 0, pbuff.Length);
            sw.Close();
        }
    }
    public void PlayPcm()
    {
        try
        {
            if (audioS.clip == null)
            {
                XMDebug.Log(this + "->PlayPcm:Begin");
                ///赋值音频
                audioS.clip = clip;
                ///播放音频
                audioS.Play();
            }
            else
            {
                XMDebug.Log(this + "->PlayPcm:Resume");
            }
            XMDebug.Log(this + "->PlayPcm:Pcm Lens：" + curAudioTime);
        }
        catch (Exception es)
        {
            XMDebug.LogError(this + "->PlayPcm:" + es.Message);
        }
    }

    /// <summary>
    /// 停止PCM播放
    /// </summary>
    public void StopPCM()
    {
        writeAudioPos = 0;
        audioS.Stop();
        audioS.clip = null;
        curAudioTime = 0;
        AudioClip.Destroy(clip);
        clip = null;
        isPcmLoadDone = false;
        XMDebug.Log(this + "->StopPCM");
    }
}
