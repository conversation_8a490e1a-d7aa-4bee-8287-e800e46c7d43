
namespace XM.Core
{
    public struct URLData
    {
        /// <summary>
        /// URL开发地址
        /// </summary>
        public readonly static string URLRootPath = "https://ai-paas-ytj.uban360.com";


        /// <summary>
        /// URL测试地址
        /// </summary>
        //public readonly static string URLRootPath = "https://aio.uban360.com";

        /// <summary>
        /// URL生产环境
        /// </summary>
        //public readonly static string URLRootPath = "https://allinone.uban360.com";


        //public readonly static string URLRootPath = "https://yapi.shinemo.com/mock/2417";
        //https://yapi.shinemo.com/mock/2417/aipaas-cgw/baas-authlink-facade/qr/login/scan
        /// <summary>
        /// 扫描二维码结果发送地址
        /// </summary>
        public readonly static string URLScanQRReaultPath = "/aipaas-cgw/baas-authlink-facade/qr/login/scan";

        /// <summary>
        /// 检查用户是否确认地址
        /// </summary>
        public readonly static string URLCheckUserEnterConfirmPath = "/aipaas-cgw/baas-authlink-facade/qr/login/check_confirm_status";

        /// <summary>
        /// 获取用户应用列表地址
        /// </summary>
        //public readonly static string URLGetApplicationListPath = "/aipaas-cgw/ai-paas/biz/app/ar/list";

        /// <summary>
        /// 获取用户系统应用列表地址
        /// </summary>
        //public readonly static string URLGetApplicationSystemListPath = "/aipaas-cgw/ai-paas/biz/app/ar/biz/app/list";

        /// <summary>
        /// 获取AR所有应用列表
        /// </summary>
        public readonly static string URLGetApplicationARAllListPath = "/aipaas-cgw/ai-paas/biz/app/ar/added/list";

        /// <summary>
        /// 获取应用是否可以进入
        /// </summary>
        public readonly static string URLGetApplicationItemIsEnter = "/aipaas-cgw/ai-paas/biz/app/ar/check/bizApp";

        /// <summary>
        /// 获取智能体是否可以进入
        /// </summary>
        public readonly static string URLGetAIAgentItemIsEnter = "/aipaas-cgw/ai-paas/app/checkApp";

        /// <summary>
        /// 获取用户信息接口地址
        /// </summary>
        public readonly static string URLGetUserInfoPath = "/aipaas-cgw/baas-authlink-facade/user/getUserInfo";

        /// <summary>
        /// 获取对话场景链接场景的Token凭证
        /// </summary>
        public readonly static string URLGetClientCredentialsAccess_tokenPath = "/aipaas-cgw/baas-authlink-facade/oauth2/clientCredentials/access_token";

        /// <summary>
        /// 创建对话地址
        /// </summary>
        public readonly static string URLCreateConversationPath = "/aipaas-cgw/ai-paas/open/chat/v2/conversation/create";

        /// <summary>
        /// 发送消息给智能体地址
        /// </summary>
        public readonly static string URLSendMessageToAgentPath = "/aipaas-cgw/ai-paas/stream/open/savePreview";

        /// <summary>
        /// 获取PPT列表地址
        /// </summary>
        public readonly static string URLGetPPTListPath = "/aipaas-cgw/ai-ppt/ppt/pageByView";

        /// <summary>
        /// 获取提词器列表
        /// </summary>
        public readonly static string URLGetAutocueListPath = "/aipaas-cgw/ai-paas/v2/knowledge/document/queryDocuments";

        /// <summary>
        /// 获取当前选择的提词器数据
        /// </summary>
        public readonly static string URLGetCurrentChoiceAutocueInfo = "/aipaas-cgw/ai-paas/v2/knowledge/document/queryChunks";

        /// <summary>
        /// 提词器校验接口
        /// </summary>
        public readonly static string URLCheckAutocueIsEnter = "/aipaas-cgw/ai-paas/v2/knowledge/document/queryDocumentDetail";

        /// <summary>
        /// 获取提词器检测文本的Id
        /// </summary>
        public readonly static string URLGetAutocueCheckTextId = "/aipaas-cgw/ai-paas/v2/knowledge/retrievalChunks";

        /// <summary>
        /// Obs地址
        /// </summary>
        public readonly static string URLObsPath = "/aipaas-cgw/ai-tools/api/v1/oss-storage/object/download?obs_key=";

        /// <summary>
        /// SST地址
        /// </summary>
        public readonly static string URLSSTPath = "http://**************:34100";
    }

    public struct URLConstant
    {
        //登录COOKIE
        public const string COOKIE = "cookie";
        //过期时间
        public const string TOKEN_EXPIRY_KEY = "auth_token_expiry";
        public const string USERNAME_KEY = "username";
        // Token有效期（7天）
        public const int TOKEN_EXPIRY_DAYS = 7;

        public const string URLContentType = "Content-Type";
        public const string URLContentTypeValueApplicaitonJson = "application/json";
        public const string URLContentTypeValueAudioWav = "audio/wav";
        public const string URLCookie = "Cookie";

        public const string URLUser_Agent = "User-Agent";
        public const string URLUnity_WebRequest = "Unity-WebRequest";

        public const string URLAccessToken = "accessToken";
    }
}
