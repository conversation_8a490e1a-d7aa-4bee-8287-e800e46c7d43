{"version": 3, "targets": {".NETStandard,Version=v2.1": {"QFramework/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/QFramework.dll": {}}, "runtime": {"bin/placeholder/QFramework.dll": {}}}}}, "libraries": {"QFramework/1.0.0": {"type": "project", "path": "QFramework.csproj", "msbuildProject": "QFramework.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["QFramework >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj", "projectName": "QFramework.CoreKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\QFramework.CoreKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}