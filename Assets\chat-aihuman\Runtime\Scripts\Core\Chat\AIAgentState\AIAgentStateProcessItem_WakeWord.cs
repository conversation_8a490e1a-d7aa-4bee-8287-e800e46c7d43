﻿using System;
using System.Collections;
using System.Collections.Generic;
using MoonSharp.VsCodeDebugger.SDK;
using QFramework;
using UnityEngine;
using XM.Common;

namespace XM.Core
{
    public class AIAgentStateProcessItem_WakeWord : AIAgentStateProcessItem
    {
        protected override AIAgentStateEnum State { get => AIAgentStateEnum.WakeWord; }

        private float waitTime = 5f;

        public override void ProcessAIAgentNormalTalk(string talkInfo)
        {
            XMDebug.Log(this, $"ProcessAIAgentNormalTalk is WakeWord continue");
            ///关闭时常检测
            CloseCheckUserTalkEvent();
            ///关闭声音检测
            AIArchitecture.Interface.GetModel<ChatModel>().MicState.UnRegister(CheckMicStateChangeAction);
            ///改变智能体状态为回答问题
            AIArchitecture.Interface.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Answer;
            AIArchitecture.Interface.SendCommand(new ChatSendMessageToAgentCommand(talkInfo));
        }

        public override void ProcessAIAgentWakeWord()
        {
            XMDebug.Log(this, $"ProcessAIAgentWakeWord already");
        }

        public override void EnterAIAgentState()
        {
            XMDebug.Log(this, $"EnterAIAgentState");
            ///显示用户打招呼文案
            TypeEventSystem.Global.Send(new ChatShowUserTalkEvent() { talkInfo = UserTalkTextInfo.UserWakeWord });
            AIArchitecture.Interface.GetSystem<AIChatSystem>()._vad.SetIsCheck(false);
            ///停留0.5S进行AI智能体打招呼音频
            TimerManager.Instance.AddTimeEvent(() =>
            {
                TypeEventSystem.Global.Register<AIAgentTalkDoneEvent>(OnAIAgentTalkDoneEvent_WakeWordResponsePlayDone);
                ///播放录制好的语音
                var audioData = AIArchitecture.Interface.GetUtility<WakeWordUtility>().GetResponseAudioByte();
                TypeEventSystem.Global.Send(new ChatShowAIAgentTalkEvent() { talkInfo = audioData.info });
                AIArchitecture.Interface.GetSystem<AIChatSystem>()._ptw.OnPCMData(audioData.AudioInfo);
                AIArchitecture.Interface.GetSystem<AIChatSystem>()._ptw.PlayPcm();
                AIArchitecture.Interface.GetSystem<AIChatSystem>()._ptw.isPcmLoadDone = true;
            }, 0.5f);
        }

        private void OnAIAgentTalkDoneEvent_WakeWordResponsePlayDone(AIAgentTalkDoneEvent @event)
        {
            AIArchitecture.Interface.GetModel<ChatModel>().UserTalkState.Value = UserTalkStateEnum.AIWaiting;
            AIArchitecture.Interface.GetSystem<AIChatSystem>()._vad.SetIsCheck(true);
            TypeEventSystem.Global.UnRegister<AIAgentTalkDoneEvent>(OnAIAgentTalkDoneEvent_WakeWordResponsePlayDone);
            ///进行时常检测用户是否开启录音
            OpenCheckUserTalkEvent();
            ///注册音频开启关闭事件
            AIArchitecture.Interface.GetModel<ChatModel>().MicState.Register(CheckMicStateChangeAction);

            ///注册第一次用户没说话事件
            checkDoneAction = CheckUserNoneTalkEvent;
        }

        private int checkTimerId;
        private System.Action checkDoneAction;
        private void OpenCheckUserTalkEvent()
        {
            checkTimerId = TimerManager.Instance.AddTimeEvent(() => 
            {
                checkDoneAction?.Invoke();
            }, waitTime);
        }

        private void RefreshCheckTime()
        {
            TimerManager.Instance.RemoveTimerEvent(checkTimerId);
            checkTimerId = TimerManager.Instance.AddTimeEvent(() =>
            {
                checkDoneAction?.Invoke();
            }, waitTime);
        }

        private void CloseCheckUserTalkEvent()
        {
            TimerManager.Instance.RemoveTimerEvent(checkTimerId);
            checkTimerId = -1;
        }
        private void CheckMicStateChangeAction(MicStateEnum state)
        {
            if (state == MicStateEnum.StartRecord)
            {
                CloseCheckUserTalkEvent();
            }
            else if(state == MicStateEnum.StopCheck)
            {
                RefreshCheckTime();
            }
        }

        private void CheckUserNoneTalkEvent()
        {
            ///关闭时常检测
            CloseCheckUserTalkEvent();
            ///取消注册音频开启关闭事件
            AIArchitecture.Interface.GetModel<ChatModel>().MicState.UnRegister(CheckMicStateChangeAction);
            AIArchitecture.Interface.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Question;
        }
    }
}
