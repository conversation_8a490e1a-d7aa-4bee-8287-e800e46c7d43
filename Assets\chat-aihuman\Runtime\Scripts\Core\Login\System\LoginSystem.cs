﻿using System;
using System.Collections.Generic;
using LitJson;
using System.Text;
using QFramework;
using UnityEngine;
using XM.Common;

namespace XM.Core
{
    public class LoginSystem : AbstractSystem
    {
        bool isScaning = false;

        private string currentLoginToken = "";
        protected override void OnInit()
        {
        }

        public void InitSystem()
        {
            this.GetUtility<QRCodeScannerUtility>().OnDecodeSuccess += QRDecodeSuccess;
        }

        public void ClearSystem() 
        {
            this.GetUtility<QRCodeScannerUtility>().OnDecodeSuccess -= QRDecodeSuccess;
            isScaning = false;
        }

        /// <summary>
        /// 尝试自动登录
        /// </summary>
        public void TryAutoLogin()
        {
            string cookie = this.GetUtility<StorageLoginDataUtility>().GetCachedCookie();
            XMDebug.Log(this,$"TryAutoLogin cookie: {cookie}");
            if (string.IsNullOrEmpty(cookie))
            {
                EnterLogin();
            }
            else
            {
                ///当前缓存Cookie数据不为空
                HttpRequestManager.Instance.Get(
                    $"{URLData.URLRootPath}{URLData.URLGetUserInfoPath}",
                    (response) =>
                    {
                        if (response != null)
                        {
                            try
                            {
                                LoginUserInfoData infoData = JsonMapper.ToObject<LoginUserInfoData>(response);
                                XMDebug.Log(this, $"userData {response}");
#if UNITY_EDITOR
                                string FileUrl = Application.dataPath + "/../TextData.txt";
                                System.IO.File.WriteAllText(FileUrl, response, Encoding.UTF8);
#endif
                                infoData.CheckSuccess((flag, msg) =>
                                {
                                    if (flag)
                                    {
                                        LoginSuccess(infoData.data, cookie);
                                    }
                                    else
                                    {
                                        XMDebug.LogError(this, $"登录信息获取失败 进入扫码流程 : {msg}");
                                        EnterLogin();
                                    }
                                });
                            }
                            catch (Exception e)
                            {
                                XMDebug.LogError(this, $"登录信息获取失败 进入扫码流程 : {e.Message} ");
                                EnterLogin();
                            }
                        }
                        else
                        {
                            XMDebug.LogError(this, $"登录信息获取失败，信息为空,进入扫码流程");
                            EnterLogin();
                        }
                    },
                    (URLConstant.URLCookie, cookie)
                );
            }
        }
        /// <summary>
        /// 进入登录页
        /// </summary>
        public void EnterLogin()
        {
            XMDebug.Log(this, "EnterLogin");
            UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Scan);
            OpenCamera();
            StartScaning();
        }

        private void OpenCamera()
        {
            XMDebug.Log(this, "OpenCamera");
#if !UNITY_EDITOR
            this.GetUtility<ExternalCameraUtility>().OpenCamera();
            var tex = this.GetUtility<ExternalCameraUtility>().GetCameraTexture();
            UIKit.GetPanel<LoginUIManager>().CameraImage.texture = tex;
#endif
            UIKit.GetPanel<LoginUIManager>().OpenCameraTexture();
        }

        private void CloseCamera()
        {
            XMDebug.Log(this, "CloseCamera");
            UIKit.GetPanel<LoginUIManager>().CloseCameraTexture();
            this.GetUtility<ExternalCameraUtility>().CloseCamera();
        }

        private void StartScaning()
        {
            XMDebug.Log(this, "StartScanner");
            isScaning = true;
            var webCam = this.GetUtility<ExternalCameraUtility>().GetCameraTexture();
            if (webCam != null)
            {
                this.GetUtility<QRCodeScannerUtility>().UpdateCamColorData(webCam.GetPixels32(), webCam.width, webCam.height);
                this.GetUtility<QRCodeScannerUtility>().StartDecodeThread();
            }
        }

        private void StopScaning()
        {
            XMDebug.Log(this, "StopScaning");
            isScaning = false;
            this.GetUtility<QRCodeScannerUtility>().AbortDecodeThread();
            TimerManager.Instance.AddTimeEvent(() => 
            {
                this.GetUtility<ExternalCameraUtility>().CloseCamera();
            },0.2f);
            
        }

        private void QRDecodeSuccess(string codeInfo)
        {
            ///扫描完毕，关闭扫描以及相机
            string tokenInfo = codeInfo;
            if (IsShinemoQRCode(ref tokenInfo))
            {
                XMDebug.Log(this, $"QRDecodeSuccess codeInfo {codeInfo} token {tokenInfo}");
                //发起登录请求
                LoginQRScanReault(tokenInfo, LoginQRScanReaultEvent);
            }
            else
            {
                XMDebug.Log(this, $"QRDecodeSuccess codeInfo {codeInfo} error");
                ///扫描二维码的时候发生错误
                //UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Back);
                //TypeEventSystem.Global.Send(new LoginQRDecodeErrorEvent { errorInfo = codeInfo });
                TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = codeInfo });
            }
            StopScaning();
        }

        private void LoginQRScanReaultEvent(bool isSuc, string token, string scanInfo)
        {
            XMDebug.Log(this, $"LoginQRScanReaultEvent isSuc {isSuc}");
            if (isSuc)
            {
                ///扫码成功，等待用户确认
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Wait);
                CheckUserEnterConfirmButton(token);
            }
            else
            {
                TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = scanInfo });
            }
        }
        /// <summary>
        /// 检测讯盟二维码是否正确
        /// </summary>
        /// <param name="codeInfo"></param>
        /// <returns></returns>
        private bool IsShinemoQRCode(ref string codeInfo)
        {
            XMDebug.Log(this, $"IsShinemoQRCode codeInfo {codeInfo}");
            int index = codeInfo.IndexOf("token=");
            if (index != -1)
            {
                codeInfo = codeInfo.Substring(index);
                codeInfo = codeInfo.Substring(codeInfo.IndexOf("=") + 1);
                return true;
            }
            return false;
        }


        /// <summary>
        /// 执行登录二维码扫描结果
        /// </summary>
        private void LoginQRScanReault(string token, Action<bool, string, string> callback)
        {
            XMDebug.Log(this, $"LoginQRScanReault token {token}");
            // 构造登录数据
            var loginData = new Dictionary<string, string>
            {
                { "token", token },
            };

            string jsonData = JsonUtility.ToJson(loginData);

            ///暂时找不到解决方案，先使用URL+Token的方式Post传输
            StringBuilder sb = new StringBuilder();
            sb.Append(URLData.URLRootPath);
            sb.Append(URLData.URLScanQRReaultPath);
            sb.Append($"?token={token}");
            string loginPath = sb.ToString();

            // 发送登录请求
            HttpRequestManager.Instance.Post(
                loginPath,
                jsonData,
                (response) =>
                {
                    if (response != null)
                    {
                        try
                        {
                            string info = System.Text.Encoding.UTF8.GetString(response.downloadHandler.data);
                            XMDebug.Log(this, $"response {info}");
                            // 解析响应数据（假设返回包含token和expiry）
                            var responseData = JsonMapper.ToObject<LoginResponse>(info);
                            XMDebug.Log(this, $"response {response}");
                            bool isSuc = responseData.CheckQRScanSuccess();
                            currentLoginToken = token;
                            callback?.Invoke(isSuc, token, info);
                        }
                        catch (Exception e)
                        {
                            XMDebug.LogError(this, $"登录响应解析错误: {e.Message}");
                            callback?.Invoke(false, token, e.Message);
                        }
                    }
                    else
                    {
                        callback?.Invoke(false, token, "Null");
                    }
                },
                (URLConstant.URLContentType, URLConstant.URLContentTypeValueApplicaitonJson)
            );
        }

        /// <summary>
        /// 检测用户是否点击确认按钮
        /// </summary>
        private void CheckUserEnterConfirmButton(string token)
        {
            XMDebug.Log(this, $"CheckUserEnterConfirmButton token {token}");
            var loginData = new Dictionary<string, string>
            {
                { "token", token },
            };

            string jsonData = JsonUtility.ToJson(loginData);

            StringBuilder sb = new StringBuilder();
            sb.Append(URLData.URLRootPath);
            sb.Append(URLData.URLCheckUserEnterConfirmPath);
            sb.Append($"?token={token}");
            string confirmPath = sb.ToString();

            HttpRequestManager.Instance.StartPolling(confirmPath, jsonData, PollingCheckUserEnterConfirmButtonSuccess, PollingCheckUserEnterConfirmButtonFail);
        }

        private void PollingCheckUserEnterConfirmButtonSuccess(string info, string cookie)
        {
            XMDebug.Log(this, $"PollingCheckUserEnterConfirmButtonSuccess info {info} cookie {cookie}");
            var responseData = JsonMapper.ToObject<LoginCheckUserEnterConfirmResponse>(info);
            responseData.CheckUserEnterConfirmSuccess((flag, code, info) =>
            {
                if (flag)
                {
                    JsonData loginData = responseData.data;
                    JsonData loginTokenData = loginData["loginToken"];
                    string loginTokenCookie = ((string)loginTokenData);
                    XMDebug.Log(this, $"cookie {cookie} ");
                    ///确认成功
                    HttpRequestManager.Instance.StopPolling();
                    // 缓存Token和用户名
                    this.GetUtility<StorageLoginDataUtility>().SaveCookie(cookie);

                    // 登录成功处理
                    OnAuthSuccess(currentLoginToken, cookie);
                }
                else
                {
                    ///只有在状态为已扫码的状态才会进行等待，其他状态返回为错误状态
                    if (code != (int)UserEnterConfirmState.Scanned)
                    {
                        ///确认失败
                        HttpRequestManager.Instance.StopPolling();
                        string errorT = "";
                        switch (code)
                        {
                            case -1:
                                errorT = "二维码过期";
                                break;
                            case -2:
                                errorT = info;
                                break;
                            case 4:
                                errorT = "用户取消";
                                break;
                            default:
                                errorT = info;
                                break;
                        }
                        
                        TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = errorT });
                    }
                }
            });
        }

        private void PollingCheckUserEnterConfirmButtonFail(string info)
        {
            XMDebug.Log(this, $"PollingCheckUserEnterConfirmButtonFail info {info}");
            ///确认失败
            HttpRequestManager.Instance.StopPolling();
            
            TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = info });
        }

        /// <summary>
        /// 登录成功处理
        /// </summary>
        private void OnAuthSuccess(string token, string cookie)
        {
            XMDebug.Log(this, $"OnAuthSuccess token {token} cookie {cookie}");
            //获取用户的登录信息
            HttpRequestManager.Instance.Get(
                $"{URLData.URLRootPath}{URLData.URLGetUserInfoPath}",
                (response) =>
                {
                    if (response != null)
                    {
                        XMDebug.Log(this, $"OnAuthSuccess response {response}");

#if UNITY_EDITOR
                        string FileUrl = $"{Application.dataPath}/../TextData.txt";
                        System.IO.File.WriteAllText(FileUrl, response, Encoding.UTF8);
#endif

                        LoginUserInfoData infoData = JsonMapper.ToObject<LoginUserInfoData>(response);
                        infoData.CheckSuccess((flag, msg) =>
                        {
                            if (flag)
                            {
                                LoginSuccess(infoData.data, cookie);
                            }
                            else
                            {
                                XMDebug.LogError(this,$"登录信息获取失败: {msg}");
                                
                                TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = $"登录信息获取失败: {msg}" });
                            }
                        });
                    }
                    else
                    {
                        XMDebug.LogError($"登录信息获取失败，信息为空");
                        TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = false, info = "登录信息获取失败，信息为空"});
                    }
                }
            );
        }

        /// <summary>
        /// 登录成功
        /// </summary>
        /// <param name="data"></param>
        /// <param name="cookie"></param>
        public void LoginSuccess(UserData data, string cookie)
        {
            XMDebug.Log(this, $"LoginSuccess cookie {cookie}");
            this.GetModel<UserModel>().UserData = data;
            this.GetModel<UserModel>().UserCookie = cookie;

            ///后续可能需要把非个人的身份排在前面
            //if (data.GetAllUserIdentityInfo().Count > 1)
            //{
            //    this.GetModel<UserModel>().currentSelectUserIdentityOrgCode = data.GetUserIdentityInfoToNextNoPerson().orgCode;
            //}
            //else
            //{
            //    this.GetModel<UserModel>().currentSelectUserIdentityOrgCode = data.GetUserIdentityInfoToOrgCode(true).orgCode;
            //}
            this.GetModel<UserModel>().CurrentSelectUserIdentityOrgCode = data.GetUserIdentityInfoToOrgCode(true).orgCode;
            TypeEventSystem.Global.Send(new LoginResultEvent() { isSuc = true});
            try
            {
                CloseCamera();
            }
            catch (Exception ex)
            {
                XMDebug.LogError(this, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 检查Token是否过期
        /// </summary>
        private bool IsTokenExpired()
        {
            string expiryString = PlayerPrefs.GetString(URLConstant.TOKEN_EXPIRY_KEY, string.Empty);

            if (string.IsNullOrEmpty(expiryString))
                return true;

            if (DateTime.TryParse(expiryString, out DateTime expiryDate))
            {
                return DateTime.Now > expiryDate;
            }
            return true;
        }

        /// <summary>
        /// 登出
        /// </summary>
        public void Logout()
        {
            XMDebug.Log(this, $"Logout");
            // 清除缓存
            this.GetUtility<StorageLoginDataUtility>().ClearStorageLoginDataCache();
            this.GetModel<UserModel>().Clear();
        }

        public void UpdateCamera() 
        {
            if (isScaning)
            {
                var webCam = this.GetUtility<ExternalCameraUtility>().GetCameraTexture();
                if (webCam != null)
                {
                    this.GetUtility<QRCodeScannerUtility>().UpdateCamColorData(webCam.GetPixels32(), webCam.width, webCam.height);
                }
            }

#if UNITY_EDITOR
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Wait);
            }
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Fail);
            }
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Scan);
            }
            if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                UIKit.GetPanel<LoginUIManager>().SwitchLoginUI(LoginUIType.Back);
            }
            if (Input.GetKeyDown(KeyCode.W))
            {
                //QRDecodeSuccess(testLoginQRUrl + testLoginQRToken);
            }
#endif
        }

#if UNITY_EDITOR
        public void TestLoginQRDecode(string testLoginQRUrl, string testLoginQRToken) 
        {
            QRDecodeSuccess(testLoginQRUrl + testLoginQRToken);
        }
#endif
    }
}
