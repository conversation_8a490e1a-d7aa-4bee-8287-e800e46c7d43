ManifestFileVersion: 0
CRC: 1170053330
Hashes:
  AssetFileHash:
    serializedVersion: 2
    Hash: 11b112e77ee8dd32d4c5fd5b480f18b1
  TypeTreeHash:
    serializedVersion: 2
    Hash: df224d222c67f9b459760957ce4072b9
  IncrementalBuildHash:
    serializedVersion: 2
    Hash: 11b112e77ee8dd32d4c5fd5b480f18b1
HashAppended: 0
ClassTypes:
- Class: 1
  Script: {instanceID: 0}
- Class: 21
  Script: {instanceID: 0}
- Class: 28
  Script: {instanceID: 0}
- Class: 48
  Script: {instanceID: 0}
- Class: 65
  Script: {instanceID: 0}
- Class: 114
  Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1344c3c82d62a2a41a3576d8abb8e3ea, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: d27e3c01b9f9d5f49988108c8bd6264d, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: e4efd14688096c1429b6015b4718399e, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 732b68102ccfe1c4d9443f955999dac6, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 75de07227afa5d941876d7cbeef64733, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 8a8695521f0d02e499659fee002a26c2, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 8d313d72b8fb8bb42a7eb042ecc32094, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 14537d7afd7fbab40b9bdcc9472d8e01, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1ce93eaae70c90d4ebac0ee2f5d7631c, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 5a9523b478072684ebe8eee707e94174, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: d1ff61bb56b25c54981292a84d5e8118, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 710f042b5394a214aa695e8c89b10302, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 41c9bb1d861bcd94a8e7ac9f845d56f8, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fea91cbfec6e0404aba4918f9a1fcb6b, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: a1a1f5cc3e8da4c46a9619b8309f0b30, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 141109fb25f0083469e71ce8cb3e75c8, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 604d9c753f04da14bb8c1b24a2426b49, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: cfe4a0fe02f6f314a9a9f1037d2af021, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: ebf6be864e9b19746ae8e9a162c557be, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 983d068900c6a7b45b5b95791c5648a7, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: dad35537af945ff46bddc5ef6f0a4b46, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 9077f11b84bc3624a853b2b3a393d56a, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 4e75c985ea2bf864d99df8509634d4d3, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 2a4db7a114972834c8e4117be1d82ba3, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: b8072c966dd0f1d41bf27c1b878b5d49, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 2adcbba2db7d6aa4ab02eb459e098b5a, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 7569abd49656aee4bacc520e1bcbbfe1, type: 3}
- Class: 115
  Script: {instanceID: 0}
- Class: 128
  Script: {instanceID: 0}
- Class: 213
  Script: {instanceID: 0}
- Class: 222
  Script: {instanceID: 0}
- Class: 224
  Script: {instanceID: 0}
- Class: 687078895
  Script: {instanceID: 0}
SerializeReferenceClassIdentifiers:
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Circle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.GradientEffect
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Hexagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.NStarPolygon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Pentagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Rectangle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Triangle
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.Events.PersistentCallGroup
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.RectOffset
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.AnimationTriggers
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ColorBlock
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.FontData
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Navigation
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ScrollRect/ScrollRectEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Scrollbar/ScrollEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.SpriteState
Assets:
- Assets/chat-aihuman/Runtime/Prefab/UI/Login/LoginUIManager.prefab
Dependencies: []
