using UnityEngine;
using QFramework;

namespace XM.Core
{
    /// <summary>
    /// 简化版移动端镜面反射
    /// 支持URP和标准渲染管线
    /// 使用Cubemap和屏幕空间反射的混合方案
    /// </summary>
    public class SimpleMirrorReflection : MonoBehaviour
    {
        [Header("反射设置")]
        [SerializeField] private Material mirrorMaterial;
        [SerializeField] private Cubemap reflectionCubemap;
        [SerializeField] private bool useRealtimeReflection = true;
        [SerializeField] private float reflectionIntensity = 1.0f;
        [SerializeField] private float fresnelPower = 1.0f;
        
        [Header("性能优化")]
        [SerializeField] private int updateFrequency = 30; // 每秒更新次数
        [SerializeField] private bool enableLOD = true;
        [SerializeField] private float lodDistance = 50f;
        
        [Header("移动端优化")]
        [SerializeField] private bool enableMobileOptimization = true;
        [SerializeField] private MobileReflectionMode mobileMode = MobileReflectionMode.Cubemap;

        [Header("渲染管线")]
        [SerializeField] private RenderPipelineType renderPipeline = RenderPipelineType.Auto;
        
        public enum MobileReflectionMode
        {
            Cubemap,        // 使用预烘焙Cubemap
            ScreenSpace,    // 屏幕空间反射
            Hybrid          // 混合模式
        }

        public enum RenderPipelineType
        {
            Auto,           // 自动检测
            Standard,       // 标准渲染管线
            URP            // Universal Render Pipeline
        }
        
        private Camera reflectionCamera;
        private RenderTexture reflectionTexture;
        private float lastUpdateTime;
        private float updateInterval;
        private bool isInitialized = false;
        private RenderPipelineType currentPipeline;
        
        private void Start()
        {
            Initialize();
        }
        
        private void Initialize()
        {
            updateInterval = 1f / updateFrequency;

            // 检测渲染管线
            DetectRenderPipeline();

            // 获取或创建材质
            if (mirrorMaterial == null)
            {
                var renderer = GetComponent<Renderer>();
                if (renderer != null)
                {
                    mirrorMaterial = renderer.material;
                }
            }

            // 移动端优化
            if (enableMobileOptimization)
            {
                ApplyMobileOptimizations();
            }
            
            // 根据模式初始化
            switch (mobileMode)
            {
                case MobileReflectionMode.Cubemap:
                    InitializeCubemapReflection();
                    break;
                case MobileReflectionMode.ScreenSpace:
                    InitializeScreenSpaceReflection();
                    break;
                case MobileReflectionMode.Hybrid:
                    InitializeHybridReflection();
                    break;
            }
            
            isInitialized = true;
        }

        private void DetectRenderPipeline()
        {
            if (renderPipeline == RenderPipelineType.Auto)
            {
                // 自动检测当前使用的渲染管线
                #if UNITY_2019_3_OR_NEWER
                var currentRP = UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset;
                if (currentRP != null)
                {
                    string rpName = currentRP.GetType().Name;
                    if (rpName.Contains("Universal") || rpName.Contains("URP"))
                    {
                        currentPipeline = RenderPipelineType.URP;
                    }
                    else
                    {
                        currentPipeline = RenderPipelineType.Standard;
                    }
                }
                else
                {
                    currentPipeline = RenderPipelineType.Standard;
                }
                #else
                currentPipeline = RenderPipelineType.Standard;
                #endif
            }
            else
            {
                currentPipeline = renderPipeline;
            }

            //XMDebug.Log(this, $"检测到渲染管线: {currentPipeline}");
        }
        
        private void ApplyMobileOptimizations()
        {
            // 根据设备性能调整设置
            int memorySize = SystemInfo.systemMemorySize;
            
            if (memorySize < 2000) // 2GB以下
            {
                updateFrequency = 15;
                mobileMode = MobileReflectionMode.Cubemap;
                useRealtimeReflection = false;
            }
            else if (memorySize < 4000) // 4GB以下
            {
                updateFrequency = 20;
                if (mobileMode == MobileReflectionMode.ScreenSpace)
                {
                    mobileMode = MobileReflectionMode.Hybrid;
                }
            }
            
            updateInterval = 1f / updateFrequency;
        }
        
        private void InitializeCubemapReflection()
        {
            if (reflectionCubemap == null)
            {
                // 创建默认Cubemap
                CreateDefaultCubemap();
            }
            
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetTexture("_ReflectionTex", reflectionCubemap);
                mirrorMaterial.SetFloat("_ReflectionIntensity", reflectionIntensity);
                mirrorMaterial.SetFloat("_FresnelPower", fresnelPower);
            }
        }
        
        private void InitializeScreenSpaceReflection()
        {
            // 创建反射纹理
            int textureSize = enableMobileOptimization ? 256 : 512;
            reflectionTexture = new RenderTexture(textureSize, textureSize, 16)
            {
                name = "SimpleMirrorReflection",
                format = RenderTextureFormat.RGB565,
                filterMode = FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Clamp
            };
            
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetTexture("_ReflectionTex", reflectionTexture);
            }
        }
        
        private void InitializeHybridReflection()
        {
            // 混合模式：近距离使用屏幕空间，远距离使用Cubemap
            InitializeCubemapReflection();
            InitializeScreenSpaceReflection();
        }
        
        private void CreateDefaultCubemap()
        {
            // 创建一个简单的天空盒Cubemap
            reflectionCubemap = RenderSettings.skybox?.GetTexture("_Tex") as Cubemap;
            
            if (reflectionCubemap == null)
            {
                // 如果没有天空盒，创建一个默认的蓝色Cubemap
                reflectionCubemap = CreateSolidColorCubemap(new Color(0.5f, 0.7f, 1.0f, 1.0f));
            }
        }
        
        private Cubemap CreateSolidColorCubemap(Color color)
        {
            Cubemap cubemap = new Cubemap(64, TextureFormat.RGB24, false);
            Color[] colors = new Color[64 * 64];
            
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i] = color;
            }
            
            for (int face = 0; face < 6; face++)
            {
                cubemap.SetPixels(colors, (CubemapFace)face);
            }
            
            cubemap.Apply();
            return cubemap;
        }
        
        private void Update()
        {
            if (!isInitialized || !useRealtimeReflection)
                return;
                
            // 帧率控制
            if (Time.time - lastUpdateTime < updateInterval)
                return;
                
            lastUpdateTime = Time.time;
            
            // LOD检查
            if (enableLOD && !IsInLODRange())
                return;
                
            UpdateReflection();
        }
        
        private bool IsInLODRange()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
                return false;
                
            float distance = Vector3.Distance(transform.position, mainCamera.transform.position);
            return distance <= lodDistance;
        }
        
        private void UpdateReflection()
        {
            switch (mobileMode)
            {
                case MobileReflectionMode.ScreenSpace:
                    UpdateScreenSpaceReflection();
                    break;
                case MobileReflectionMode.Hybrid:
                    UpdateHybridReflection();
                    break;
                // Cubemap模式不需要实时更新
            }
        }
        
        private void UpdateScreenSpaceReflection()
        {
            if (reflectionTexture == null)
                return;
                
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
                return;
                
            // 简化的屏幕空间反射
            // 这里可以实现更复杂的SSR算法
            Graphics.Blit(null, reflectionTexture, mirrorMaterial);
        }
        
        private void UpdateHybridReflection()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
                return;
                
            float distance = Vector3.Distance(transform.position, mainCamera.transform.position);
            
            // 根据距离选择反射模式
            if (distance < lodDistance * 0.5f)
            {
                // 近距离使用屏幕空间反射
                UpdateScreenSpaceReflection();
                if (mirrorMaterial != null)
                {
                    mirrorMaterial.SetTexture("_ReflectionTex", reflectionTexture);
                }
            }
            else
            {
                // 远距离使用Cubemap
                if (mirrorMaterial != null)
                {
                    mirrorMaterial.SetTexture("_ReflectionTex", reflectionCubemap);
                }
            }
        }
        
        /// <summary>
        /// 设置反射强度
        /// </summary>
        public void SetReflectionIntensity(float intensity)
        {
            reflectionIntensity = intensity;
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetFloat("_ReflectionIntensity", intensity);
            }
        }
        
        /// <summary>
        /// 设置Fresnel强度
        /// </summary>
        public void SetFresnelPower(float power)
        {
            fresnelPower = power;
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetFloat("_FresnelPower", power);
            }
        }
        
        /// <summary>
        /// 切换反射模式
        /// </summary>
        public void SetReflectionMode(MobileReflectionMode mode)
        {
            mobileMode = mode;
            
            switch (mode)
            {
                case MobileReflectionMode.Cubemap:
                    InitializeCubemapReflection();
                    break;
                case MobileReflectionMode.ScreenSpace:
                    InitializeScreenSpaceReflection();
                    break;
                case MobileReflectionMode.Hybrid:
                    InitializeHybridReflection();
                    break;
            }
        }
        
        /// <summary>
        /// 启用/禁用实时反射
        /// </summary>
        public void EnableRealtimeReflection(bool enable)
        {
            useRealtimeReflection = enable;
        }
        
        private void OnDestroy()
        {
            if (reflectionTexture != null)
            {
                DestroyImmediate(reflectionTexture);
            }
            
            if (reflectionCamera != null)
            {
                DestroyImmediate(reflectionCamera.gameObject);
            }
        }
        
        private void OnValidate()
        {
            if (Application.isPlaying && isInitialized)
            {
                SetReflectionIntensity(reflectionIntensity);
                SetFresnelPower(fresnelPower);
            }
        }
    }
}
