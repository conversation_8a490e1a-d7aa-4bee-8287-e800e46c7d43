# 移动端镜面反射材质使用说明

## 概述

这是一个专为移动端优化的镜面反射材质系统，同时支持URP和标准渲染管线，包含以下组件：

### URP版本：
1. **MobileMirrorReflection.shader** - URP镜面反射Shader
2. **SimpleMirrorReflection.cs** - 简化版反射管理器
3. **MirrorReflectionUtility.cs** - 完整版反射工具类
4. **MirrorReflectionEditor.cs** - Editor工具

### 标准管线版本：
1. **StandardMobileMirrorReflection.shader** - 标准管线镜面反射Shader
2. **StandardMirrorReflection.cs** - 标准管线反射工具类
3. **StandardMirrorReflectionEditor.cs** - 标准管线Editor工具

## 快速开始

### 1. 选择合适的版本

**自动检测（推荐）**
- 系统会自动检测当前使用的渲染管线
- URP项目自动使用URP版本
- 标准管线项目自动使用标准管线版本

**手动选择**
- **URP项目**: 使用 `SimpleMirrorReflection` 组件和 `XM/Mobile/MirrorReflection` Shader
- **标准管线项目**: 使用 `StandardMirrorReflection` 组件和 `XM/Standard/MobileMirrorReflection` Shader

### 2. 创建镜面材质

**方法一：使用菜单（自动检测）**
- 在Unity菜单栏选择 `XM Tools > Mirror Reflection > Create Mirror Material`
- 系统会自动选择合适的Shader并创建材质

**方法二：使用特定管线菜单**
- URP: `XM Tools > Mirror Reflection > Create Mirror Material`
- 标准管线: `XM Tools > Mirror Reflection > Create Standard Mirror Material`

**方法三：手动创建**
- 在Project窗口右键 `Create > Material`
- 根据渲染管线选择对应的Shader：
  - URP: `XM/Mobile/MirrorReflection`
  - 标准管线: `XM/Standard/MobileMirrorReflection`

### 3. 添加反射组件

**方法一：使用菜单（自动检测）**
- 选择要添加镜面反射的GameObject
- 在Unity菜单栏选择 `XM Tools > Mirror Reflection > Add Mirror Reflection`

**方法二：使用特定管线菜单**
- URP: `XM Tools > Mirror Reflection > Add Mirror Reflection`
- 标准管线: `XM Tools > Mirror Reflection > Add Standard Mirror Reflection`

**方法三：手动添加**
- 选择GameObject，在Inspector中点击 `Add Component`
- 根据渲染管线添加对应组件：
  - URP: `SimpleMirrorReflection`
  - 标准管线: `StandardMirrorReflection`

### 4. 配置反射设置

**URP版本 (SimpleMirrorReflection组件)：**
- **镜面材质**: 拖入刚创建的镜面材质
- **反射模式**: 根据设备性能选择
  - `Cubemap`: 适用于低端设备
  - `ScreenSpace`: 适用于高端设备
  - `Hybrid`: 适用于中端设备
- **反射强度**: 控制反射效果的强度 (0-2)
- **菲涅尔强度**: 控制边缘反射效果 (0.1-5)

**标准管线版本 (StandardMirrorReflection组件)：**
- **反射层级**: 设置参与反射的层级
- **禁用像素光源**: 提高反射渲染性能
- **纹理大小**: 反射纹理分辨率 (64-2048)
- **渲染路径**: Forward/VertexLit/Deferred
- **启用雾效**: 反射中是否包含雾效
- **启用天空盒**: 反射中是否包含天空盒

## 性能优化

### 设备分级建议

**低端设备 (内存 < 2GB)**
```csharp
// 推荐设置
mobileMode = MobileReflectionMode.Cubemap;
useRealtimeReflection = false;
updateFrequency = 15;
reflectionIntensity = 0.5f;
```

**中端设备 (内存 2-4GB)**
```csharp
// 推荐设置
mobileMode = MobileReflectionMode.Hybrid;
useRealtimeReflection = true;
updateFrequency = 20;
reflectionIntensity = 0.8f;
```

**高端设备 (内存 > 4GB)**
```csharp
// 推荐设置
mobileMode = MobileReflectionMode.ScreenSpace;
useRealtimeReflection = true;
updateFrequency = 30;
reflectionIntensity = 1.0f;
```

### 性能优化技巧

1. **启用LOD**: 距离较远时自动降低反射质量
2. **控制更新频率**: 根据需要调整反射更新的FPS
3. **使用移动端优化**: 自动根据设备性能调整设置
4. **合理设置反射距离**: 避免不必要的远距离反射计算

## Shader参数说明

### 基础设置
- `_MainTex`: 基础纹理
- `_BaseColor`: 基础颜色
- `_Metallic`: 金属度 (0-1)
- `_Smoothness`: 光滑度 (0-1)

### 反射设置
- `_ReflectionTex`: 反射纹理
- `_ReflectionIntensity`: 反射强度 (0-2)
- `_FresnelPower`: 菲涅尔强度 (0.1-5)
- `_FresnelBias`: 菲涅尔偏移 (0-1)

### 法线设置
- `_BumpMap`: 法线贴图
- `_BumpScale`: 法线强度 (0-2)

### 扰动设置
- `_DistortionStrength`: 扰动强度 (0-0.1)

### 移动端优化
- `_EnableReflection`: 启用/禁用反射
- `_EnableNormalMap`: 启用/禁用法线贴图
- `_LODLevel`: LOD级别 (0-3)

## 代码示例

### 运行时控制反射

```csharp
public class MirrorController : MonoBehaviour
{
    private SimpleMirrorReflection mirrorReflection;
    
    void Start()
    {
        mirrorReflection = GetComponent<SimpleMirrorReflection>();
    }
    
    // 根据设备性能自动调整
    void AutoAdjustQuality()
    {
        int memorySize = SystemInfo.systemMemorySize;
        
        if (memorySize < 2000)
        {
            mirrorReflection.SetReflectionMode(SimpleMirrorReflection.MobileReflectionMode.Cubemap);
            mirrorReflection.EnableRealtimeReflection(false);
        }
        else if (memorySize < 4000)
        {
            mirrorReflection.SetReflectionMode(SimpleMirrorReflection.MobileReflectionMode.Hybrid);
        }
        else
        {
            mirrorReflection.SetReflectionMode(SimpleMirrorReflection.MobileReflectionMode.ScreenSpace);
        }
    }
    
    // 动态调整反射强度
    void AdjustReflectionIntensity(float intensity)
    {
        mirrorReflection.SetReflectionIntensity(intensity);
    }
}
```

### 性能监控

```csharp
public class ReflectionPerformanceMonitor : MonoBehaviour
{
    private SimpleMirrorReflection mirrorReflection;
    private float lastFPS;
    
    void Start()
    {
        mirrorReflection = GetComponent<SimpleMirrorReflection>();
    }
    
    void Update()
    {
        // 监控帧率，自动调整质量
        float currentFPS = 1.0f / Time.deltaTime;
        
        if (currentFPS < 30f && lastFPS >= 30f)
        {
            // 帧率下降，降低反射质量
            mirrorReflection.SetReflectionMode(SimpleMirrorReflection.MobileReflectionMode.Cubemap);
        }
        else if (currentFPS > 45f && lastFPS <= 45f)
        {
            // 帧率提升，可以提高反射质量
            mirrorReflection.SetReflectionMode(SimpleMirrorReflection.MobileReflectionMode.Hybrid);
        }
        
        lastFPS = currentFPS;
    }
}
```

## 注意事项

1. **兼容性**: 确保项目使用URP渲染管线
2. **性能**: 在低端设备上建议使用Cubemap模式
3. **内存**: 实时反射会增加内存使用，注意监控
4. **层级**: 合理设置反射层级，避免不必要的对象参与反射
5. **更新频率**: 根据实际需要调整反射更新频率

## 故障排除

### 常见问题

**Q: 反射效果不显示**
A: 检查以下项目：
- 确认使用了正确的Shader
- 检查反射纹理是否正确设置
- 确认反射强度不为0
- 检查材质是否正确应用到对象上

**Q: 性能问题**
A: 尝试以下优化：
- 降低反射纹理分辨率
- 减少更新频率
- 启用LOD
- 使用Cubemap模式替代实时反射

**Q: 反射效果不真实**
A: 调整以下参数：
- 增加菲涅尔强度
- 调整扰动强度
- 使用合适的法线贴图
- 调整金属度和光滑度

## 版本历史

- v1.0: 初始版本，支持基础镜面反射
- v1.1: 添加移动端优化
- v1.2: 增加混合反射模式
- v1.3: 添加Editor工具和自动优化

## 技术支持

如有问题，请联系开发团队或查看项目文档。
