﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2022_3_56;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_FONT_ENGINE_1_6_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;NoWakeWord;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>2022.3.56f1c1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="E:\UnityPro\AIHuman\chat-aihuman_Inmo\com.unity.logging@1.0.16\Runtime\SourceGenerators\MainLoggingGenerator.dll" />
    <Analyzer Include="E:\UnityPro\AIHuman\chat-aihuman_Inmo\com.unity.logging@1.0.16\Runtime\SourceGenerators\LoggingCommon.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\InmoInput\InputManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\StorageLoginDataUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Time\TimerManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\UnityEditor\UserIdentityUIConfig.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\ParserToken.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\ScrollContentText.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\ARAutocue\Command\ARAutocueCommand.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_Wait.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\TTS&amp;&amp;STT\TTSSSTVADEnum\TTSSSTVADEnum.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mesh\EZTriangulator.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Model\TensorelModel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonData.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\Plugins\Netly\Netly.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\TestScrollDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Net\HKUnityWebRequest.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Login\Controller\LoginController.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\ExternalCameraUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\MirrorReflectionUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\Controller\AIChatController.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMSelectUIItem_Button.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\JsonTool.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMUIEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\URLData.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\ApplicationFate.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItemScrollView.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMARAutoChoiceUI\XMARAutocueChoiceItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\XMSEETools.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mono\MonoManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\ILoginUIItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\CommonSeverityErrorPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMLattice_ScrollButton.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonReader.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\AIChatUIAIAgentChoicePanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_PPTChoice.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\Text\LoginUIItemDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\UIQRScanPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\Unit.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\UnityEditor\LocalAppConfig.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem_WakeWord.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\AIAgentTalkTextData.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMARAutoChoiceUI\XMARAutocueModeChoiceItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMIdentityChoiceUI\XMIdentityChoiceItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_ReLogin.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_Back.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\PPT\Controller\PPTController.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\LocalAppUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMPPTChoiceUI\XMPPTChoiceItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMSelectUIItem_ApplicationItem.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\Scripts\FlipPageCircularScrollView.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\ExtensionMethod.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\UI\CheckUIPointerClickImage.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mesh\MeshGenerator.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMLattice_Button.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\FileSizeTool.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AutocueUI\AutocueStateItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\ARAutocue\Event\AutocueEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AutocueUI\AutocueSectionItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\MathTool.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\TestOtherScrollTypeDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_AIAgent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\Text\LoginApplicationUIItemNameText.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_ARAutocueChoice.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Log\XMDebug.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Manager\HttpRequestManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\UI\CheckUIDragImage.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Test\TalkAnimDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\InmoInput\UI\LatticeInteraction\LatticeButton.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\UnityEditor\AIAgentUIConfig.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Shader\StandardModel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem_WaitTalk.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem_Answer.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mono\MonoController.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\AIChatUIEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Load\LoadResourcesManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\PPT\Event\PPTEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\ARAutocue\Controller\ARAutocueController.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\AIHumanUserData.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\VoiceInputs.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMARApplicationScroll.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\STTUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\TTS&amp;&amp;STT\Event\TTSSSTVADEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AutocueUI\AutocueModeChoiceUIPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\WakeWordUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_Fail.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Test\AudioTestDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\PPT\Command\PPTCommand.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\Command\ChatCommand.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\UI\DelayButton.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\Anim\MicAnim.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\Scripts\ExpandTipsCircularScrollView.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\TTS&amp;&amp;STT\VAD.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\Scripts\ExpandCircularScrollView.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\Lexer.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\InmoInput\UI\LatticeInteraction\LatticeBrain.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\ToAudio.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\PPT\PPTPrepare.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\TipsPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\TTS&amp;&amp;STT\VADParameters.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\AIArchitecture.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\InmoInput\InputEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\InmoAir3SDK\Scenes\SDKSample.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\SceneUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem_Sleep.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Login\System\LoginSystem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\DateTimeTool.cs" />
    <Compile Include="Assets\chat-aihuman\Add-In\UGUICircularScrollView\Scripts\UICircularScrollView.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_IdentityChoice.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\Anim\ScanLineMoveAnim.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIInterface.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Login\Event\LoginEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\LoginData.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\AIChatUIChatPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMSelectUIItem_AIAgentItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\SEETools.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Login\Command\LoginCommand.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\TTS&amp;&amp;STT\PCMToWAV.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_Application.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Net\GptStreamWrapper.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mono\EventSystemMar.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMSelectUIItem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\UnityMainThreadDispatcher.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\Text\LoginPPTNameText.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\Enum\TensorelEnum.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_BackApplication.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\InmoInput\UI\LatticeInteraction\LatticeDemo.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\LoginUI\LoginUIItem_Scan.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Net\NwtWork.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\SimpleMirrorReflection.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mesh\PolygonDrawer.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\System\AIChatSystem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\QRCodeScannerUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AutocueUI\AutocueUIPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\AIChatUIManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\UnityEditor\SystemUIConfig.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\AIAgentState\AIAgentStateProcessItem_Question.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\Event\ChatEvent.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\ARAutocue\System\ARAutocueSystem.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\TalkFrame.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mesh\MeshCombine.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\AIChatUI\AIChatUIErrorPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\FileTool.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\ImgTool.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\StandardMirrorReflection.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\TTSUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Tool\StringTools.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Command\CommandManager.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\UISpritePoolUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Chat\ChatPrepare.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\PPTUI\PPTUIPanel.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Utility\PDFUtility.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\Mesh\Triangulation.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Common\LitJson\JsonException.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Login\LoginPrepare.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\Data\AIChatData.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\UnityEditor\AIAgentLocalAudioConfig.cs" />
    <Compile Include="Assets\chat-aihuman\Runtime\Scripts\Core\UI\XMSelelctUI\XMLattice_ChoiceButton.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\InmoAir3SDK\Resources\Materials\RingRayPoint.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AIAgentLocalAudio\question\question1.txt" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\CSCore.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\ApplicationPost.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AutocueLocalData\autocueDemo2.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AutocueLocalData\autocueDemo3.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Model\胡桃\胡桃.MMD4Mecanim.xml" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AutocueLocalData\autocueJsonDemo.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AutocueLocalData\autocueDemo.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Font\FontShader.shader" />
    <None Include="Assets\chat-aihuman\Add-In\MPUIKit\readme.txt" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\NAudio-Unity.dll" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\AIAgentLocalAudio\response\response1.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Model\胡桃\readme【一定要看】.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Shader\MobileMirrorReflection.shader" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\Netly\Byter.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Font\PingFang\7000汉字 符号 英文字符集.txt" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\ARAutocueTestData.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Font\PingFang\字库.txt" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\Android\AndroidManifest.xml" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\Netly\Netly.dll" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\Netly\readme.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Resources\ApplicationPostOrgCode.txt" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Model\胡桃\胡桃.xml" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\chat-aihuman\Runtime\Art\Shader\StandardMobileMirrorReflection.shader" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\InmoAir3SDK\Resources\Materials\BinocularRenderShader.shader" />
    <None Include="Assets\chat-aihuman\Add-In\Plugins\zxing.unity.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@2.1.4\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CSCore">
      <HintPath>Assets\chat-aihuman\Add-In\Plugins\CSCore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library\PackageCache\com.unity.testtools.codecoverage@1.2.6\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NAudio-Unity">
      <HintPath>Assets\chat-aihuman\Add-In\Plugins\NAudio-Unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Byter">
      <HintPath>Assets\chat-aihuman\Add-In\Plugins\Netly\Byter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Netly">
      <HintPath>Assets\chat-aihuman\Add-In\Plugins\Netly\Netly.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="inmolib_common">
      <HintPath>Assets\Plugins\DLL\inmolib_common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Markdig">
      <HintPath>Assets\chat-aihuman\Runtime\Scripts\Common\Framework\QFramework\Toolkits\_CoreKit\PackageKit\Markdown\Markdig.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@1.11.4\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>Assets\chat-aihuman\Add-In\Plugins\zxing.unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\soft\Unity\2022.3.56f1c1\2022.3.56f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PrimeTween.Runtime">
      <HintPath>Library\ScriptAssemblies\PrimeTween.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.MetaQuestSupport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management">
      <HintPath>Library\ScriptAssemblies\Unity.XR.Management.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.ConformanceAutomation">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.ConformanceAutomation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.OculusQuestSupport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils">
      <HintPath>Library\ScriptAssemblies\Unity.XR.CoreUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.MetaQuestSupport">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.MetaQuestSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Performance.Profile-Analyzer.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Performance.Profile-Analyzer.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.Management.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.CoreUtils.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.OculusQuestSupport">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.OculusQuestSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.EditorCoroutines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniRx">
      <HintPath>Library\ScriptAssemblies\UniRx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="BoundfoxStudios.FluentAssertions">
      <HintPath>Library\ScriptAssemblies\BoundfoxStudios.FluentAssertions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XR.LegacyInputHelpers">
      <HintPath>Library\ScriptAssemblies\UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\UniTask.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.DOTween">
      <HintPath>Library\ScriptAssemblies\UniTask.DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.Addressables">
      <HintPath>Library\ScriptAssemblies\UniTask.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>Library\ScriptAssemblies\UnityEditor.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>Library\ScriptAssemblies\UnityEngine.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XR.LegacyInputHelpers">
      <HintPath>Library\ScriptAssemblies\UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask.Linq">
      <HintPath>Library\ScriptAssemblies\UniTask.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Model">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NAudio.Core">
      <HintPath>Library\ScriptAssemblies\NAudio.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.RuntimeDebugger.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniTask">
      <HintPath>Library\ScriptAssemblies\UniTask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PrimeTween.Editor">
      <HintPath>Library\ScriptAssemblies\PrimeTween.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="ResKit.Editor.csproj" />
    <ProjectReference Include="Paroxe.PDFRenderer.csproj" />
    <ProjectReference Include="Mochineko.VoiceActivityDetection.Samples.csproj" />
    <ProjectReference Include="Mochineko.VoiceActivityDetection.csproj" />
    <ProjectReference Include="MPUIKit.Editor.csproj" />
    <ProjectReference Include="PrimeTween.Installer.csproj" />
    <ProjectReference Include="SupportOldQF.csproj" />
    <ProjectReference Include="Mochineko.VoiceActivityDetection.Components.csproj" />
    <ProjectReference Include="MPUIKit.csproj" />
    <ProjectReference Include="Unity.Logging.csproj" />
    <ProjectReference Include="UIKit.csproj" />
    <ProjectReference Include="QFramework.CoreKit.csproj" />
    <ProjectReference Include="QFramework.csproj" />
    <ProjectReference Include="ResKit.csproj" />
    <ProjectReference Include="GifForUnity.csproj" />
    <ProjectReference Include="BestHTTP.csproj" />
    <ProjectReference Include="GifImporter.Editor.csproj" />
    <ProjectReference Include="Paroxe.PDFRenderer.Editor.csproj" />
    <ProjectReference Include="UIKit.Editor.csproj" />
    <ProjectReference Include="AudioKit.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
