
using QFramework;
using UnityEngine;
using XM.Common;

namespace XM.Core
{
    public class ARAutocueController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController
    {
        public string id;
        public string talkDemoInfo;
        void Start()
        {
            this.SendCommand<ARAutocueLoadCommand>();
        }

        void Update() 
        {
            if (Input.GetKeyDown(KeyCode.W))
            {
                //UIKit.GetPanel<AutocueUIPanel>().SetSection(id);
            }
            if (Input.GetKeyDown(KeyCode.E))
            {
                this.SendCommand(new ARAutocueParagraphMatchingCommand(talkDemoInfo));
            }
        }
        void OnEnable()
        {
            Register();
        }

        void OnDisable()
        {
            UnRegister();
        }
        void Register()
        {
            TypeEventSystem.Global.Register<VADRecordClipEvent>(OnVADRecordClipEvent);
            TypeEventSystem.Global.Register<VADIsActiveEvent>(OnVADIsActiveEvent);
            TypeEventSystem.Global.Register<AutocueCheckAudioIsHumanResultEvent>(OnAutocueCheckAudioIsHumanResultEvent);
            TypeEventSystem.Global.Register<AutocueModeUIChoiceDoneEvent>(OnAutocueModeUIChoiceDoneEvent);
            

            this.GetModel<AutocueModel>().AutocueAIMode.Register(OnAutocueAIModeChangeEvent);
            
            InputManager.Instance.OnBack += OnBackEvent;
            InputManager.Instance.OnTripleTap += OnOnTripleTapEvent;
        }

        void UnRegister()
        {
            TypeEventSystem.Global.UnRegister<VADRecordClipEvent>(OnVADRecordClipEvent);
            TypeEventSystem.Global.UnRegister<VADIsActiveEvent>(OnVADIsActiveEvent);
            TypeEventSystem.Global.UnRegister<AutocueCheckAudioIsHumanResultEvent>(OnAutocueCheckAudioIsHumanResultEvent);
            TypeEventSystem.Global.UnRegister<AutocueModeUIChoiceDoneEvent>(OnAutocueModeUIChoiceDoneEvent);

            this.GetModel<AutocueModel>().AutocueAIMode.UnRegister(OnAutocueAIModeChangeEvent);

            InputManager.Instance.OnBack -= OnBackEvent;
            InputManager.Instance.OnTripleTap -= OnOnTripleTapEvent;
        }

        private void OnVADRecordClipEvent(VADRecordClipEvent @event)
        {
            XMDebug.Log(this, $"OnVADRecordClipEvent");
            this.SendCommand(new ARAutocueAudioClipDoneCommand(@event._clip));
        }
        private void OnVADIsActiveEvent(VADIsActiveEvent @event)
        {
            XMDebug.Log(this, $"OnVADIsActiveEvent isActive {@event.isActive}");
        }


        private void OnAutocueCheckAudioIsHumanResultEvent(AutocueCheckAudioIsHumanResultEvent @event)
        {
            if (@event.isHuman)
            {
                XMDebug.Log(this, $"OnAutocueCheckAudioIsHumanResultEvent IsHuman info {@event.info}");
                this.SendCommand(new ARAutocueParagraphMatchingCommand(@event.info));
            }
            else
            {
                XMDebug.Log(this, $"OnAutocueCheckAudioIsHumanResultEvent NoHuman");
            }
        }

        private void OnAutocueModeUIChoiceDoneEvent(AutocueModeUIChoiceDoneEvent @event)
        {
            XMDebug.Log(this, $"OnAutocueModeUIChoiceDoneEvent");
            this.SendCommand(new ARAutocueAIModeUIChoiceDoneCommand(@event.isAI));
        }

        private void OnAutocueAIModeChangeEvent(bool obj)
        {
            XMDebug.Log(this, $"OnAutocueAIModeChangeEvent AIModeChange {obj}");
            this.SendCommand(new ARAutocueAIModeChangeCommand(obj));
        }


        private void OnBackEvent()
        {
            XMDebug.Log(this, $"OnBackEvent");
            this.SendCommand<ARAutocueOnBackCommand>();
        }

        private void OnOnTripleTapEvent()
        {
            XMDebug.Log(this, $"OnTripleTapRayneoEvent");
            this.SendCommand<ARAutocueTripleTapEventCommand>();
        }

        public IArchitecture GetArchitecture()
        {
            return AIArchitecture.Interface;
        }
    }
}
