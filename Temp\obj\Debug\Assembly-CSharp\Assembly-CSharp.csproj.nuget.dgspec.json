{"format": 1, "restore": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj": {}}, "projects": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj", "projectName": "AudioKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\AudioKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj", "projectName": "BestHTTP", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\BestHTTP\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj", "projectName": "GifForUnity", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\GifForUnity\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj", "projectName": "GifImporter.Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\GifImporter.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj", "projectName": "Mochineko.VoiceActivityDetection.Components", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Mochineko.VoiceActivityDetection.Components\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj", "projectName": "Mochineko.VoiceActivityDetection", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Mochineko.VoiceActivityDetection\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj", "projectName": "Mochineko.VoiceActivityDetection.Samples", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Mochineko.VoiceActivityDetection.Samples\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj", "projectName": "MPUIKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\MPUIKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj", "projectName": "MPUIKit.Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\MPUIKit.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj", "projectName": "Paroxe.PDFRenderer", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Paroxe.PDFRenderer\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj", "projectName": "Paroxe.PDFRenderer.Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Paroxe.PDFRenderer.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj", "projectName": "PrimeTween.Installer", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\PrimeTween.Installer\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj", "projectName": "QFramework.CoreKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\QFramework.CoreKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj", "projectName": "QFramework", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\QFramework\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj", "projectName": "ResKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\ResKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj", "projectName": "ResKit.Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\ResKit.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj", "projectName": "SupportOldQF", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\SupportOldQF\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj", "projectName": "UIKit", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\UIKit\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj", "projectName": "UIKit.Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\UIKit.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj", "projectName": "Unity.Logging", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Unity.Logging\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}