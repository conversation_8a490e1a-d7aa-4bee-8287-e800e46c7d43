#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using XM.Core;

namespace XM.Editor
{
    /// <summary>
    /// 镜面反射材质编辑器
    /// </summary>
    [CustomEditor(typeof(SimpleMirrorReflection))]
    public class SimpleMirrorReflectionEditor : UnityEditor.Editor
    {
        private SimpleMirrorReflection mirrorReflection;
        
        private SerializedProperty mirrorMaterial;
        private SerializedProperty reflectionCubemap;
        private SerializedProperty useRealtimeReflection;
        private SerializedProperty reflectionIntensity;
        private SerializedProperty fresnelPower;
        private SerializedProperty updateFrequency;
        private SerializedProperty enableLOD;
        private SerializedProperty lodDistance;
        private SerializedProperty enableMobileOptimization;
        private SerializedProperty mobileMode;
        
        private void OnEnable()
        {
            mirrorReflection = (SimpleMirrorReflection)target;
            
            mirrorMaterial = serializedObject.FindProperty("mirrorMaterial");
            reflectionCubemap = serializedObject.FindProperty("reflectionCubemap");
            useRealtimeReflection = serializedObject.FindProperty("useRealtimeReflection");
            reflectionIntensity = serializedObject.FindProperty("reflectionIntensity");
            fresnelPower = serializedObject.FindProperty("fresnelPower");
            updateFrequency = serializedObject.FindProperty("updateFrequency");
            enableLOD = serializedObject.FindProperty("enableLOD");
            lodDistance = serializedObject.FindProperty("lodDistance");
            enableMobileOptimization = serializedObject.FindProperty("enableMobileOptimization");
            mobileMode = serializedObject.FindProperty("mobileMode");
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("移动端镜面反射设置", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 基础设置
            EditorGUILayout.LabelField("基础设置", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(mirrorMaterial, new GUIContent("镜面材质"));
            EditorGUILayout.PropertyField(reflectionCubemap, new GUIContent("反射Cubemap"));
            EditorGUILayout.PropertyField(useRealtimeReflection, new GUIContent("启用实时反射"));
            
            EditorGUILayout.Space();
            
            // 反射参数
            EditorGUILayout.LabelField("反射参数", EditorStyles.boldLabel);
            EditorGUILayout.Slider(reflectionIntensity, 0f, 2f, new GUIContent("反射强度"));
            EditorGUILayout.Slider(fresnelPower, 0.1f, 5f, new GUIContent("菲涅尔强度"));
            
            EditorGUILayout.Space();
            
            // 性能设置
            EditorGUILayout.LabelField("性能优化", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(enableMobileOptimization, new GUIContent("启用移动端优化"));
            
            if (enableMobileOptimization.boolValue)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(mobileMode, new GUIContent("移动端模式"));
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.IntSlider(updateFrequency, 1, 60, new GUIContent("更新频率 (FPS)"));
            
            EditorGUILayout.PropertyField(enableLOD, new GUIContent("启用LOD"));
            if (enableLOD.boolValue)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(lodDistance, new GUIContent("LOD距离"));
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space();
            
            // 设备信息
            DrawDeviceInfo();
            
            EditorGUILayout.Space();
            
            // 快速设置按钮
            DrawQuickSetupButtons();
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private void DrawDeviceInfo()
        {
            EditorGUILayout.LabelField("设备信息", EditorStyles.boldLabel);
            
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.TextField("GPU", SystemInfo.graphicsDeviceName);
            EditorGUILayout.TextField("内存", $"{SystemInfo.systemMemorySize} MB");
            EditorGUILayout.TextField("GPU内存", $"{SystemInfo.graphicsMemorySize} MB");
            EditorGUILayout.TextField("平台", Application.platform.ToString());
            EditorGUI.EndDisabledGroup();
            
            // 推荐设置
            string recommendation = GetRecommendedSettings();
            if (!string.IsNullOrEmpty(recommendation))
            {
                EditorGUILayout.HelpBox(recommendation, MessageType.Info);
            }
        }
        
        private string GetRecommendedSettings()
        {
            int memorySize = SystemInfo.systemMemorySize;
            
            if (memorySize < 2000)
            {
                return "检测到低端设备，建议使用Cubemap模式，关闭实时反射";
            }
            else if (memorySize < 4000)
            {
                return "检测到中端设备，建议使用混合模式，降低更新频率";
            }
            else if (memorySize >= 6000)
            {
                return "检测到高端设备，可以使用屏幕空间反射模式";
            }
            
            return "";
        }
        
        private void DrawQuickSetupButtons()
        {
            EditorGUILayout.LabelField("快速设置", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("低端设备设置"))
            {
                ApplyLowEndSettings();
            }
            
            if (GUILayout.Button("中端设备设置"))
            {
                ApplyMidEndSettings();
            }
            
            if (GUILayout.Button("高端设备设置"))
            {
                ApplyHighEndSettings();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("创建镜面材质"))
            {
                CreateMirrorMaterial();
            }
            
            if (GUILayout.Button("应用到选中对象"))
            {
                ApplyToSelectedObjects();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void ApplyLowEndSettings()
        {
            enableMobileOptimization.boolValue = true;
            mobileMode.enumValueIndex = (int)SimpleMirrorReflection.MobileReflectionMode.Cubemap;
            useRealtimeReflection.boolValue = false;
            updateFrequency.intValue = 15;
            reflectionIntensity.floatValue = 0.5f;
            enableLOD.boolValue = true;
            lodDistance.floatValue = 30f;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void ApplyMidEndSettings()
        {
            enableMobileOptimization.boolValue = true;
            mobileMode.enumValueIndex = (int)SimpleMirrorReflection.MobileReflectionMode.Hybrid;
            useRealtimeReflection.boolValue = true;
            updateFrequency.intValue = 20;
            reflectionIntensity.floatValue = 0.8f;
            enableLOD.boolValue = true;
            lodDistance.floatValue = 50f;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void ApplyHighEndSettings()
        {
            enableMobileOptimization.boolValue = false;
            mobileMode.enumValueIndex = (int)SimpleMirrorReflection.MobileReflectionMode.ScreenSpace;
            useRealtimeReflection.boolValue = true;
            updateFrequency.intValue = 30;
            reflectionIntensity.floatValue = 1.0f;
            enableLOD.boolValue = true;
            lodDistance.floatValue = 100f;
            
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
        }
        
        private void CreateMirrorMaterial()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建镜面材质",
                "MirrorMaterial",
                "mat",
                "选择保存位置");

            if (!string.IsNullOrEmpty(path))
            {
                // 检测当前渲染管线
                string shaderName = GetShaderNameForCurrentPipeline();
                Shader mirrorShader = Shader.Find(shaderName);

                if (mirrorShader != null)
                {
                    Material material = new Material(mirrorShader);
                    AssetDatabase.CreateAsset(material, path);
                    AssetDatabase.SaveAssets();

                    mirrorMaterial.objectReferenceValue = material;
                    serializedObject.ApplyModifiedProperties();

                    EditorGUIUtility.PingObject(material);
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", $"找不到镜面反射Shader: {shaderName}", "确定");
                }
            }
        }

        private string GetShaderNameForCurrentPipeline()
        {
            #if UNITY_2019_3_OR_NEWER
            var currentRP = UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset;
            if (currentRP != null)
            {
                string rpName = currentRP.GetType().Name;
                if (rpName.Contains("Universal") || rpName.Contains("URP"))
                {
                    return "XM/Mobile/MirrorReflection";
                }
            }
            #endif
            return "XM/Standard/MobileMirrorReflection";
        }
        
        private void ApplyToSelectedObjects()
        {
            GameObject[] selectedObjects = Selection.gameObjects;
            
            foreach (GameObject obj in selectedObjects)
            {
                if (obj.GetComponent<SimpleMirrorReflection>() == null)
                {
                    obj.AddComponent<SimpleMirrorReflection>();
                }
            }
            
            EditorUtility.DisplayDialog("完成", $"已为 {selectedObjects.Length} 个对象添加镜面反射组件", "确定");
        }
    }
    
    /// <summary>
    /// 镜面反射工具菜单
    /// </summary>
    public class MirrorReflectionTools
    {
        [MenuItem("XM Tools/Mirror Reflection/Add Mirror Reflection")]
        public static void AddMirrorReflection()
        {
            GameObject[] selectedObjects = Selection.gameObjects;
            
            if (selectedObjects.Length == 0)
            {
                EditorUtility.DisplayDialog("提示", "请先选择要添加镜面反射的对象", "确定");
                return;
            }
            
            foreach (GameObject obj in selectedObjects)
            {
                if (obj.GetComponent<SimpleMirrorReflection>() == null)
                {
                    obj.AddComponent<SimpleMirrorReflection>();
                }
            }
            
            EditorUtility.DisplayDialog("完成", $"已为 {selectedObjects.Length} 个对象添加镜面反射组件", "确定");
        }
        
        [MenuItem("XM Tools/Mirror Reflection/Create Mirror Material")]
        public static void CreateMirrorMaterial()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "创建镜面材质",
                "MirrorMaterial",
                "mat",
                "选择保存位置");

            if (!string.IsNullOrEmpty(path))
            {
                string shaderName = GetShaderNameForCurrentPipeline();
                Shader mirrorShader = Shader.Find(shaderName);

                if (mirrorShader != null)
                {
                    Material material = new Material(mirrorShader);
                    AssetDatabase.CreateAsset(material, path);
                    AssetDatabase.SaveAssets();

                    EditorGUIUtility.PingObject(material);
                    Selection.activeObject = material;
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", $"找不到镜面反射Shader: {shaderName}", "确定");
                }
            }
        }

        private static string GetShaderNameForCurrentPipeline()
        {
            #if UNITY_2019_3_OR_NEWER
            var currentRP = UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset;
            if (currentRP != null)
            {
                string rpName = currentRP.GetType().Name;
                if (rpName.Contains("Universal") || rpName.Contains("URP"))
                {
                    return "XM/Mobile/MirrorReflection";
                }
            }
            #endif
            return "XM/Standard/MobileMirrorReflection";
        }
    }
}
#endif
