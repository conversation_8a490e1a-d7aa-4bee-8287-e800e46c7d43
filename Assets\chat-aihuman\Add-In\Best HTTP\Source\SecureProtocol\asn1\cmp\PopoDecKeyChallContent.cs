﻿#if !BESTHTTP_DISABLE_ALTERNATE_SSL && (!UNITY_WEBGL || UNITY_EDITOR)
#pragma warning disable
using System;

using BestHTTP.SecureProtocol.Org.BouncyCastle.Utilities;

namespace BestHTTP.SecureProtocol.Org.BouncyCastle.Asn1.Cmp
{
	public class PopoDecKeyChallContent
	    : Asn1Encodable
	{
	    private readonly Asn1Sequence content;

	    private PopoDecKeyChallContent(Asn1Sequence seq)
	    {
	        content = seq;
	    }

	    public static PopoDecKeyChallContent GetInstance(object obj)
	    {
	        if (obj is PopoDecKeyChallContent)
	            return (PopoDecKeyChallContent)obj;

			if (obj is Asn1Sequence)
	            return new PopoDecKeyChallContent((Asn1Sequence)obj);

            throw new ArgumentException("Invalid object: " + BestHTTP.SecureProtocol.Org.BouncyCastle.Utilities.Platform.GetTypeName(obj), "obj");
	    }

	    public virtual Challenge[] ToChallengeArray()
	    {
	        Challenge[] result = new Challenge[content.Count];
	        for (int i = 0; i != result.Length; ++i)
	        {
	            result[i] = Challenge.GetInstance(content[i]);
	        }
	        return result;
	    }

	    /**
	     * <pre>
	     * PopoDecKeyChallContent ::= SEQUENCE OF Challenge
	     * </pre>
	     * @return a basic ASN.1 object representation.
	     */
	    public override Asn1Object ToAsn1Object()
	    {
	        return content;
	    }
	}
}
#pragma warning restore
#endif
