ManifestFileVersion: 0
CRC: 418519611
Hashes:
  AssetFileHash:
    serializedVersion: 2
    Hash: ab4bdba157531aa716fc01453fc30272
  TypeTreeHash:
    serializedVersion: 2
    Hash: 4934690ccc93d77cd6d064c9cc26cb22
  IncrementalBuildHash:
    serializedVersion: 2
    Hash: ab4bdba157531aa716fc01453fc30272
HashAppended: 0
ClassTypes:
- Class: 1
  Script: {instanceID: 0}
- Class: 21
  Script: {instanceID: 0}
- Class: 28
  Script: {instanceID: 0}
- Class: 48
  Script: {instanceID: 0}
- Class: 114
  Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 2fa1e314e593d914a83b4ddb9bc34f8c, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 6940cdda447048efa58605bd546f9920, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: beed0ac07f8dd224d9d9027437c52f94, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: ceb8df676d3c5164bad7dc2d9dab03bd, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 141109fb25f0083469e71ce8cb3e75c8, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: dd18bcae91dc5d74fbf4200820712e18, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 1c3f106a78e1aa142b294d8d1a41114b, type: 3}
- Class: 115
  Script: {instanceID: 0}
- Class: 128
  Script: {instanceID: 0}
- Class: 213
  Script: {instanceID: 0}
- Class: 222
  Script: {instanceID: 0}
- Class: 224
  Script: {instanceID: 0}
- Class: 225
  Script: {instanceID: 0}
SerializeReferenceClassIdentifiers:
- AssemblyName: GifForUnity
  ClassName: GifImporter.GifFrame
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Circle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.GradientEffect
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Hexagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.NStarPolygon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Pentagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Rectangle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Triangle
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.Events.PersistentCallGroup
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.RectOffset
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.AnimationTriggers
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Button/ButtonClickedEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ColorBlock
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.FontData
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.Navigation
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.ScrollRect/ScrollRectEvent
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.SpriteState
Assets:
- Assets/chat-aihuman/Runtime/Prefab/UI/AIChat/AIChatUIChatPanel.prefab
Dependencies: []
