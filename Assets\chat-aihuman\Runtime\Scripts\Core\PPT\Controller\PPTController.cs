using System;
using System.Collections;
using System.Collections.Generic;
using QFramework;
using UnityEngine;
using XM.Common;

namespace XM.Core
{
    public class PPTController : Mono<PERSON><PERSON>aviour, IController
    {

        void Start() 
        {
            this.SendCommand<PPTLoadCommand>();
        }

        void OnEnable()
        {
            Register();
        }

        void OnDisable()
        {
            UnRegister();
        }
        void Register()
        {
            TypeEventSystem.Global.Register<PPTLoadPPTDoneEvent>(OnPPTLoadPPTDoneEvent);
            TypeEventSystem.Global.Register<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);
            TypeEventSystem.Global.Register<TripleTapRayneoEvent>(OnTripleTapRayneoEvent);

            this.GetModel<PPTModel>().CurrentPPTIndex.Register(OnCurrentPPTIndexChangeEvent);
            InputManager.Instance.OnBack += OnBackEvent;
            InputManager.Instance.OnRightArrow += OnRightArrowEvent;
            InputManager.Instance.OnLeftArrow += OnLeftArrowEvent;
        }


        void UnRegister()
        {
            TypeEventSystem.Global.UnRegister<PPTLoadPPTDoneEvent>(OnPPTLoadPPTDoneEvent);
            TypeEventSystem.Global.UnRegister<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);
            TypeEventSystem.Global.UnRegister<TripleTapRayneoEvent>(OnTripleTapRayneoEvent);

            this.GetModel<PPTModel>().CurrentPPTIndex.UnRegister(OnCurrentPPTIndexChangeEvent);
            InputManager.Instance.OnBack -= OnBackEvent;
            InputManager.Instance.OnRightArrow -= OnRightArrowEvent;
            InputManager.Instance.OnLeftArrow -= OnLeftArrowEvent;
        }
        private void OnPPTLoadPPTDoneEvent(PPTLoadPPTDoneEvent @event)
        {
            XMDebug.Log(this, $"OnPPTLoadPPTDoneEvent isSuc {@event.isSuc}");
            if (@event.isSuc)
            {
                this.SendCommand<PPTLoadDoneCommand>();
            }
            else
            {
                UIKit.OpenPanel<AIChatUIErrorPanel>(new AIChatUIErrorPanellUIData() { errorInfo =  @event.errorInfo});
            }
        }

        private void OnDoubleTapRayneoEvent(DoubleTapRayneoEvent @event)
        {
            XMDebug.Log(this, $"OnDoubleTapRayneoEvent");
        }
        private void OnTripleTapRayneoEvent(TripleTapRayneoEvent @event)
        {
            XMDebug.Log(this, $"OnTripleTapRayneoEvent");
        }
        private void OnCurrentPPTIndexChangeEvent(int obj)
        {
            XMDebug.Log(this, $"OnCurrentPPTIndexChangeEvent index {obj}");
            if (obj != -1)
                this.SendCommand<PPTUpdatePageCommand>();
        }

        private void OnBackEvent()
        {
            XMDebug.Log(this, $"OnBackEvent");
            this.SendCommand<PPTExitSceneCommand>();
        }
        private void OnRightArrowEvent()
        {
            XMDebug.Log(this, $"OnRightArrowEvent");
            this.SendCommand<PPTNextPageCommand>();
        }
        private void OnLeftArrowEvent()
        {
            XMDebug.Log(this, $"OnLeftArrowEvent");
            this.SendCommand<PPTBackPageCommand>();
        }

        public IArchitecture GetArchitecture()
        {
            return AIArchitecture.Interface;
        }
    }
}
