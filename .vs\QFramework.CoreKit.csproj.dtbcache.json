{"RootPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo", "ProjectFileName": "QFramework.CoreKit.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageMaker\\Command\\PublishPackageCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerExit2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\RegistrationPolicies\\DefaultRegistrationPolicy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\IMGUIGraphDataCache.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Converters\\TableConversions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\ActionKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\UserDataRegistries\\TypeDescriptorRegistry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Service\\PacakgeLoginService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpPropertyAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Window\\RenderInfo\\PackageKitViewRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineAutoLink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\Singleton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerStayEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\ScopeBlockStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIFlexibleSpace.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\MonoSingletonProperty.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\BuildTimeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockHeading.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\CrossPlatformGUILayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\VerticalSplitView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ConsoleKit\\Framework\\ConsoleWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\SDK\\Utilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\LinkedListIndex.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LogKit\\LogKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnPointerEnterEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Custom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\Tools.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\Bind\\IBind.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\6.UnityEngineUIGraphicExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineHtml.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\CompositeStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Service\\IPackageLoginService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\AutoDescribingUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIRectLabelView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUICustom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\BinaryOperatorExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnInitializePotentialDragEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IO\\FileUserData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\ParameterDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Sequence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\CoroutineModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\PackageManagerState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUILabel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\SymbolRefType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIBox.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\BuildTimeScopeBlock.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Pool\\PoolableObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\DebuggerAction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Serialization\\SerializationExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventSystem\\EnumEventSystem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\APIDescriptionENAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererinlineDelimiter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LiveCodingKit\\Editor\\LiveCodingKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnPointerClickEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\8.UnityEngineVectorExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\SDK\\Protocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\IMGUILayoutRoot.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CodeAnalysis\\AstNode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIXMLView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\Docker.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRenderInlineHTMLEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Errors\\ScriptRuntimeException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\PackageManagerInitCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\LabelStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\IOptimizableDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\UpdatePackageCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\GotoStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Factory\\IObjectFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_Coroutines.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\PackageRepository.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\IUserDataType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\LoopTracker.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\EmbeddedResourcesScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\AutoSaveDump.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\Drawers\\IMGUIGraphEnumDrawer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expression_.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\ListPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\ForLoopStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\Deprecated.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\ImportPackageCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceStatistics.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\ReflectionSpecialNames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\InteropRegistrationPolicy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\CoroutineState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Model\\ClassAPIGroupRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\LuaBase_CLib.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\TableKit\\Script\\TableKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\BindablePropertyKit\\PlayerPrefsFloatProperty.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounters\\DummyPerformanceStopwatch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\TreeView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\IO\\BinDumpBinaryReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\HardwiredDescriptors\\HardwiredMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\ILocalPackageVersionModel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\UIElementExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\IUserDataMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIAreaLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_InstructionLoop.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\ICodeTemplate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Instruction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageMaker\\SingleFileCreator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionExitEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\OverloadedMethodMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\CodeGenKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\MathModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\FastStackDynamic.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\OsTimeModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Modules\\MoonSharpModuleMethodAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\ProxyObjects\\IProxyFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\StandardPlatformAccessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\StandardFileType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionStay2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphNodeEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\DebugModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\IO\\BinDumpBinaryWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Queue\\MonoActionQueue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphRenamePopup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\NameSpace_XmlHelp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIToolbarView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDHandlerNaviagte.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUISpace.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\ExprListExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\NodeBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpVisibleAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Modules\\ModuleRegister.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIToggle.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\StandardUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ConsoleKit\\LogModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\7.UnityEngineOthersExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\Internal\\IMGUIGraphRerouteReference.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\EventMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDMenus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Data\\LanguageDefineConfig.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Converters\\StringConversions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Language\\NamespaceCodeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Command\\LogoutCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\PropertyAPIAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockHtml.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Converters\\NumericConversions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\StringLib\\KopiLua_StrLib.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Repeat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Fast_Interface\\Loader_Fast.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\BuildTimeScopeFrame.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnPointerDownEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\ActionKitMonoBehaviourEvents.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionEnterEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LogKit\\QConsole.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Framework\\CodeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnDropEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\IMGUIHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\StringModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\FrameworkCore.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\Bind\\IBindOld.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_UtilityFunctions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\DebuggerCaps.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\UserData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\JsonKit\\JSONObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\MethodMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\IMDBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\DelayFrame.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LiveCodingKit\\Editor\\LiveCodingKitSetting.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnDeselectEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Stopwatch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\DynamicExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\EmptyStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\IMGUIView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\OpCodeMetadataType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Utilities\\MutableTuple.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\ClosureContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\BasicModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\CodeGenHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\WellKnownSymbols.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineLink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\Table.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpHideMemberAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\ValueTypeDefaultCtorMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Serialization\\ObjectValueConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\LocaleKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\IMDActions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LiveCodingKit\\Editor\\LiveCodingKitSettingEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\TableModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\SearchCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\InvalidScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIEnumPopup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\FunctionDefinitionStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\IMGUIAbstractLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_Errors.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIRectLabel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIVerticalLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\Guidline\\Editor\\Guideline.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\IMDLayoutBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUITreeView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\ForEachLoopStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDHistroy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\LocaleKitEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\ChunkStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\IUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\LuaBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Writer\\FileCodeWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\LocaleKitConfigView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\MemberDescriptors\\DynValueMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Model\\MethodAPIRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Utilities\\PackageKitAssemblyCache.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Language\\ClassCodeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Symbol\\OpenBraceCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\1.CSharp\\1.SystemStringExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Errors\\DynamicExpressionException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\MethodAPIAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\Bind\\Bind.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\ScriptFunctionDelegate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\InstallPackageCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\FrameworkPCL.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDContentText.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\SymbolRefExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\PropertyMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\Base\\FrameworkReflectionBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\EasyIMGUI.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineLiteral.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\HardwiredDescriptors\\HardwiredMethodMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\HardwiredDescriptors\\HardwiredUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\DataType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\WhileStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Utilities\\PackageHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnDragEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_Debugger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\MemberDescriptors\\ObjectCallbackMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\1.CSharp\\2.SystemIOExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Lexer\\LexerUtils.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphEditorBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\CallStackItem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\RefIdObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\BreakStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\MemberDescriptors\\FunctionMemberDescriptorBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\ReplaceableMonoSingleton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\ScriptOptions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\CallbackArguments.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\AbstractBindInspector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Lexer\\TokenType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnScrollEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\RootCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Serialization\\Json\\JsonTableConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\0.UnityEngineObjectExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Loop.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\TableIteratorsModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\3.UnityEngineMonoBehaviourExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\IMGUIAbstractView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\DebugContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Pool\\Pool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\IGeneratorUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\ReturnStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\WatchType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Interface\\IPackageKitView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\DescriptorHelpers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\Bind\\AbstractBind.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Condition.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IO\\StreamFileUserDataBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\Base\\FrameworkClrBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\Editor\\MoonSharpImporter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Executor\\MonoUpdateActionExecutor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\Bit32Module.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerEnter2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Command\\LoginCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Pool\\IPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\DebuggerLogic\\AsyncDebugger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\FunctionCallExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphEditorAction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\DotNetCorePlatformAccessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\APIDescriptionCNAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\CodeGenKitSetting.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\SourceCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDContentImage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerExitEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererMarkdown.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Attributes\\PackageKitGroupAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\IDebugger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IoModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\LinqHelpers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\BindSearchHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpUserDataAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnCancelEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\ScriptLoadingContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ConsoleKit\\ConsoleKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\Editor\\QFrameworkRegisterEditorTypesExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\Setting\\CodeGenKitSettingEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\IfStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\State\\PackageKitLoginState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\RenderEndCommandExecutor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\ShortCut\\AudioSourceShortCutExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\StandardEnumUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\OsSystemModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\LimitedPlatformAccessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\FunctionDefinitionExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_IExecutionContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnPointerExitEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIGenericMenu.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\ProxyObjects\\DelegateProxyFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\CallbackFunction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Lexer\\Lexer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnMoveEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\PropertyTableAssigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphRenameFixAssetProcessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphAssetModProcessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\DebuggerLogic\\MoonSharpDebugSession.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IO\\FileUserDataBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Language\\CustomCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ProxyUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\1.CSharp\\3.SystemCollectionsExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Factory\\ObjectFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDMGGifs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\ReferenceEqualityComparer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Data\\LanguageEvent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\FunctionCallStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\SourceRef.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\ISingleton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\UserDataRegistries\\ExtensionMethodsRegistry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\Infos\\BindInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\TailCallData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\SymbolRef.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\MonoSingletonPath.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\View\\RegisterView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\REPL\\ReplInterpreterScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\Editor\\UnityEditorScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\EditorHttp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Factory\\CustomObjectFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LogKit\\Depreacted.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\IOverloadableMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\ExecutionState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\Extension_Methods.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\WatchItem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Pool\\IPoolable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDViewer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\CodeGenTask.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Root\\EasyEditorWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\JsonKit\\VectorTemplates.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDBlock.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Pool\\IPoolType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\Service\\IPackageManagerServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\RegistrationPolicies\\PermanentRegistrationPolicy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Attributes\\PackageKitRenderOrderAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Components\\LocaleText.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Symbol\\CloseBraceCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IO\\BinaryEncoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\HardwiredDescriptors\\DefaultValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Factory\\DefaultObjectFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\SafeObjectPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\MemberDescriptors\\ArrayMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Language\\UsingCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\RegistrationPolicies\\IRegistrationPolicy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Writer\\ICodeWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\DispatchingUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\IPackageTypeConfigModel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\UserDataMemberType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphAndNodeEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Data\\LanguageText.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\REPL\\ReplInterpreter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Lerp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Framework\\ICode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\IVariable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\YieldRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\IndexExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\IMGUIGraphNode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Attributes\\DisplayNameENAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\DynValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Language\\CustomCodeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Symbol\\EmptyLineCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpUserDataMetamethodAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounters\\GlobalPerformanceStopwatch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\IMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphGUILayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\IMGUIGraph.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphReflection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\RepeatStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\FileSystemScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDStyleConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Model\\PropertyAPIRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Mono\\OnBecameVisibleEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ConsoleKit\\Framework\\ConsoleModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\MonoSingleton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\CharPtr.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIHorizontalLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\BindablePropertyKit\\EditorPrefsBoolProperty.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\PrefabUtils.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\Rules.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Data\\LanguageDefine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\CompositeUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\BindablePropertyKit\\PlayerPrefsBoolProperty.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\LiteralExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Converters\\ScriptToClrConversions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDBlockLine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDHandlerImages.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\IMGUIGraphNodePort.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\JsonModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDUtils.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\UnityAssetsScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\4.UnityEngineCameraExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\APIDocLocale.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\IScriptPrivateResource.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\UpdateCategoriesFromModelCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\AsyncExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GridKit\\EasyGrid.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Framework\\IActionExecutor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Attributes\\MoonSharpHiddenAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Errors\\InterpreterException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Components\\LocaleUnityEvent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnEndDragEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\PersistentMonoSingleton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Modules\\CoreModules.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\ViewControllerInspectorStyle.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDBlockContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Writer\\StringCodeWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Framework\\Code\\Framework\\ICodeScope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\Closure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\DebuggerLogic\\IAsyncDebuggerClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDStyle.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDImporter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Callback.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\ReflectionMemberDescriptors\\FieldMemberDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounters\\IPerformanceStopwatch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\IScriptLoader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\DynamicExprExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\LuaState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\SingletonProperty.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Script.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Errors\\SyntaxErrorException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\SingletonKit\\Scripts\\SingletonCreator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\ScriptGlobalOptions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\1.CSharp\\4.SystemReflectionExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\SDK\\DebugSession.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\InteropAccessMode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIScrollLayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\MultiDictionary.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Options\\ColonOperatorBehaviour.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\AdjustmentExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUITextField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\ClassAPIAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Serialization\\Json\\JsonNull.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\Editor\\MoonSharpEditorWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\ViewControllerInspector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineLineBreak.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Deprecated.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\DynamicModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Attributes\\IMGUIGraphEnum.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\ViewControllerInspectorLocale.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\IClosureBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphEditorGUI.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\IPlatformAccessor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Framework\\IMGUILayout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Modules\\MoonSharpModuleConstantAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\ErrorHandlingModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Window\\PackageKitWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_BinaryDump.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\TablePair.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Attributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockQuote.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\IMGUISceneGraph.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\OpCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\IO\\UndisposableStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\ByteCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Attributes\\DisplayNameCNAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphImporter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\CallStackItemFlags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\PackageData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\ViewControllerTemplate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\FrameworkWin8.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\DictionaryPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnPointerUpEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDPreferences.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\PackageKitLoginApp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\MetaTableModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\OtherBinds.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageMaker\\View\\PackageMakerEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\TableConstructor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\IO\\StandardIOFileUserDataBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUISceneGraphInspector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnSelectEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\PackageManagerModel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerStay2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Framework\\Attributes\\APIExampleCodeAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphPreferences.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FSMKit\\IState.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\1.UnityEngineGameObjectExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\QFramework.PackageKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Debugging\\DebugService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\DebuggerLogic\\EmptyDebugSession.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\2.UnityEngineTransformExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\RuntimeScopeFrame.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\Model\\ClassAPIRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor_Scope.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Scripts\\Components\\ViewController.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\ICodeGenTemplate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUITextArea.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\Scopes\\RuntimeScopeBlock.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\LocaleKit\\Scripts\\Actions\\LocaleKitChangeLanguageAction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\View\\PackageManagerView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\REPL\\ReplHistoryNavigator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIPopup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Window\\RenderInfo\\PackageKitScriptViewRenderInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Lexer\\Token.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDEditorAsset.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Modules\\MoonSharpModuleAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\APIVersion.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Command\\OpenRegisterViewCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\SimpleObjectPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Model\\Service\\PackageManagerServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDBlockSpace.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnTriggerEnterEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIButton.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Framework\\IAction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\IWireableDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIImageButtonView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\LoadModule.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnBeginDragEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\1.CSharp\\0.SystemObjectExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\FrameworkCLR.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Command\\OpenDetailCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\FastStack.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\Utilities\\FluentGUIStyle.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageMaker\\PackageMaker.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionExit2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererInlineEmphasis.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\RegistrationPolicies\\AutomaticRegistrationPolicy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Loaders\\ScriptLoaderBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\StandardGenericsUserDataDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\InstructionFieldUsage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockThematicBreak.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\MDAsset.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageManager\\Utilities\\UrlHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Frameworks\\Base\\FrameworkBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\PlatformAutoDetector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounters\\PerformanceStopwatch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionEnter2DEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\DebuggerLogic\\VariableInspector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\FluentAPI\\0.Unity\\5.UnityEngineColorExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PoolKit\\Scripts\\Factory\\NonPublicObjectFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\Coroutine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\CoreLib\\StringLib\\StringRange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Errors\\InternalErrorException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnSubmitEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\PredefinedUserData\\EnumerableWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\CustomConvertersCollection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\CodeGenKitPipeline.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Coroutine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\Command\\OpenRegisterWebsiteCommand.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\Root\\EasyInspectorEditor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\StandardDescriptors\\EventFacade.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\PredefinedUserData\\AnonWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\DeprecateActionKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\EditorKit\\View\\IMGUIRectBoxView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Compatibility\\Framework.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\UI\\OnUpdateSelectedEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataTypes\\TypeValidationFlags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\LuaStateInterop\\LuaLBuffer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\View\\PackageKitLoginView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\Internal\\APIDoc\\APIDoc.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\PackageMaker\\Service\\UploadPackage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\VM\\Processor\\Processor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Platforms\\PlatformAccessorBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventSystem\\StringEventSystem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\BasicDescriptors\\MemberDescriptorAccess.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Physics\\OnCollisionStayEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\DataStructs\\Slice.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Expressions\\UnaryOperatorExpression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Interop\\Converters\\ClrToScriptConversions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Login\\View\\LoginView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Diagnostics\\PerformanceCounterType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Renderer\\MDRendererBlockParagraph.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Parallel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\IOCKit\\IOCKit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\EventKit\\EventTrigger\\Mono\\OnBecameInvisibleEventTrigger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Tree\\Statements\\AssignmentStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Debugger\\MoonSharpVsCodeDebugServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\GraphKit\\IMGUI\\Scripts\\Editor\\IMGUIGraphResources.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ScriptKit\\MoonSharp\\Interpreter\\Execution\\ScriptExecutionContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\CodeGenKit\\Editor\\BindInspectorLocale.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Framework\\Attributes\\PackageKitIgnoreAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\ActionKit\\Scripts\\Internal\\Action\\Delay.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Layout\\MDBlockContainer.cs"}], "References": [{"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Netly\\Byter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\CSCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Demigiant\\DOTween\\DOTween.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\GifToUnity\\Editor\\GifPacked.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\Plugins\\DLL\\inmolib_common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\log4netPlastic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Markdig.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.nuget.mono-cecil@1.11.4\\Mono.Cecil.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\NAudio-Unity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Netly\\Netly.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@3.2.1\\Runtime\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\bin\\Debug\\QFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\bin\\Debug\\QFramework.dll"}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collections@2.1.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\ScriptAssemblies\\UnityEditor.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\VisionOSPlayer\\UnityEditor.VisionOS.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\ScriptAssemblies\\UnityEngine.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\unityplastic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\zxing.unity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\bin\\Debug\\QFramework.CoreKit.dll", "OutputItemRelativePath": "QFramework.CoreKit.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}