{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AudioKit": "1.0.0", "BestHTTP": "1.0.0", "GifForUnity": "1.0.0", "GifImporter.Editor": "1.0.0", "MPUIKit": "1.0.0", "MPUIKit.Editor": "1.0.0", "Mochineko.VoiceActivityDetection": "1.0.0", "Mochineko.VoiceActivityDetection.Components": "1.0.0", "Mochineko.VoiceActivityDetection.Samples": "1.0.0", "Paroxe.PDFRenderer": "1.0.0", "Paroxe.PDFRenderer.Editor": "1.0.0", "PrimeTween.Installer": "1.0.0", "QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0", "ResKit": "1.0.0", "ResKit.Editor": "1.0.0", "SupportOldQF": "1.0.0", "UIKit": "1.0.0", "UIKit.Editor": "1.0.0", "Unity.Logging": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "AudioKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0"}, "compile": {"bin/placeholder/AudioKit.dll": {}}, "runtime": {"bin/placeholder/AudioKit.dll": {}}}, "BestHTTP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/BestHTTP.dll": {}}, "runtime": {"bin/placeholder/BestHTTP.dll": {}}}, "GifForUnity/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/GifForUnity.dll": {}}, "runtime": {"bin/placeholder/GifForUnity.dll": {}}}, "GifImporter.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GifForUnity": "1.0.0"}, "compile": {"bin/placeholder/GifImporter.Editor.dll": {}}, "runtime": {"bin/placeholder/GifImporter.Editor.dll": {}}}, "Mochineko.VoiceActivityDetection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Logging": "1.0.0"}, "compile": {"bin/placeholder/Mochineko.VoiceActivityDetection.dll": {}}, "runtime": {"bin/placeholder/Mochineko.VoiceActivityDetection.dll": {}}}, "Mochineko.VoiceActivityDetection.Components/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mochineko.VoiceActivityDetection": "1.0.0", "Unity.Logging": "1.0.0"}, "compile": {"bin/placeholder/Mochineko.VoiceActivityDetection.Components.dll": {}}, "runtime": {"bin/placeholder/Mochineko.VoiceActivityDetection.Components.dll": {}}}, "Mochineko.VoiceActivityDetection.Samples/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mochineko.VoiceActivityDetection": "1.0.0", "Unity.Logging": "1.0.0"}, "compile": {"bin/placeholder/Mochineko.VoiceActivityDetection.Samples.dll": {}}, "runtime": {"bin/placeholder/Mochineko.VoiceActivityDetection.Samples.dll": {}}}, "MPUIKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/MPUIKit.dll": {}}, "runtime": {"bin/placeholder/MPUIKit.dll": {}}}, "MPUIKit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"MPUIKit": "1.0.0"}, "compile": {"bin/placeholder/MPUIKit.Editor.dll": {}}, "runtime": {"bin/placeholder/MPUIKit.Editor.dll": {}}}, "Paroxe.PDFRenderer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Paroxe.PDFRenderer.dll": {}}, "runtime": {"bin/placeholder/Paroxe.PDFRenderer.dll": {}}}, "Paroxe.PDFRenderer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Paroxe.PDFRenderer": "1.0.0"}, "compile": {"bin/placeholder/Paroxe.PDFRenderer.Editor.dll": {}}, "runtime": {"bin/placeholder/Paroxe.PDFRenderer.Editor.dll": {}}}, "PrimeTween.Installer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/PrimeTween.Installer.dll": {}}, "runtime": {"bin/placeholder/PrimeTween.Installer.dll": {}}}, "QFramework/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/QFramework.dll": {}}, "runtime": {"bin/placeholder/QFramework.dll": {}}}, "QFramework.CoreKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0"}, "compile": {"bin/placeholder/QFramework.CoreKit.dll": {}}, "runtime": {"bin/placeholder/QFramework.CoreKit.dll": {}}}, "ResKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0"}, "compile": {"bin/placeholder/ResKit.dll": {}}, "runtime": {"bin/placeholder/ResKit.dll": {}}}, "ResKit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0", "ResKit": "1.0.0"}, "compile": {"bin/placeholder/ResKit.Editor.dll": {}}, "runtime": {"bin/placeholder/ResKit.Editor.dll": {}}}, "SupportOldQF/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AudioKit": "1.0.0", "QFramework.CoreKit": "1.0.0", "ResKit": "1.0.0", "UIKit": "1.0.0"}, "compile": {"bin/placeholder/SupportOldQF.dll": {}}, "runtime": {"bin/placeholder/SupportOldQF.dll": {}}}, "UIKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0"}, "compile": {"bin/placeholder/UIKit.dll": {}}, "runtime": {"bin/placeholder/UIKit.dll": {}}}, "UIKit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"QFramework": "1.0.0", "QFramework.CoreKit": "1.0.0", "UIKit": "1.0.0"}, "compile": {"bin/placeholder/UIKit.Editor.dll": {}}, "runtime": {"bin/placeholder/UIKit.Editor.dll": {}}}, "Unity.Logging/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.Logging.dll": {}}, "runtime": {"bin/placeholder/Unity.Logging.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "AudioKit/1.0.0": {"type": "project", "path": "AudioKit.csproj", "msbuildProject": "AudioKit.csproj"}, "BestHTTP/1.0.0": {"type": "project", "path": "BestHTTP.csproj", "msbuildProject": "BestHTTP.csproj"}, "GifForUnity/1.0.0": {"type": "project", "path": "GifForUnity.csproj", "msbuildProject": "GifForUnity.csproj"}, "GifImporter.Editor/1.0.0": {"type": "project", "path": "GifImporter.Editor.csproj", "msbuildProject": "GifImporter.Editor.csproj"}, "Mochineko.VoiceActivityDetection/1.0.0": {"type": "project", "path": "Mochineko.VoiceActivityDetection.csproj", "msbuildProject": "Mochineko.VoiceActivityDetection.csproj"}, "Mochineko.VoiceActivityDetection.Components/1.0.0": {"type": "project", "path": "Mochineko.VoiceActivityDetection.Components.csproj", "msbuildProject": "Mochineko.VoiceActivityDetection.Components.csproj"}, "Mochineko.VoiceActivityDetection.Samples/1.0.0": {"type": "project", "path": "Mochineko.VoiceActivityDetection.Samples.csproj", "msbuildProject": "Mochineko.VoiceActivityDetection.Samples.csproj"}, "MPUIKit/1.0.0": {"type": "project", "path": "MPUIKit.csproj", "msbuildProject": "MPUIKit.csproj"}, "MPUIKit.Editor/1.0.0": {"type": "project", "path": "MPUIKit.Editor.csproj", "msbuildProject": "MPUIKit.Editor.csproj"}, "Paroxe.PDFRenderer/1.0.0": {"type": "project", "path": "Paroxe.PDFRenderer.csproj", "msbuildProject": "Paroxe.PDFRenderer.csproj"}, "Paroxe.PDFRenderer.Editor/1.0.0": {"type": "project", "path": "Paroxe.PDFRenderer.Editor.csproj", "msbuildProject": "Paroxe.PDFRenderer.Editor.csproj"}, "PrimeTween.Installer/1.0.0": {"type": "project", "path": "PrimeTween.Installer.csproj", "msbuildProject": "PrimeTween.Installer.csproj"}, "QFramework/1.0.0": {"type": "project", "path": "QFramework.csproj", "msbuildProject": "QFramework.csproj"}, "QFramework.CoreKit/1.0.0": {"type": "project", "path": "QFramework.CoreKit.csproj", "msbuildProject": "QFramework.CoreKit.csproj"}, "ResKit/1.0.0": {"type": "project", "path": "ResKit.csproj", "msbuildProject": "ResKit.csproj"}, "ResKit.Editor/1.0.0": {"type": "project", "path": "ResKit.Editor.csproj", "msbuildProject": "ResKit.Editor.csproj"}, "SupportOldQF/1.0.0": {"type": "project", "path": "SupportOldQF.csproj", "msbuildProject": "SupportOldQF.csproj"}, "UIKit/1.0.0": {"type": "project", "path": "UIKit.csproj", "msbuildProject": "UIKit.csproj"}, "UIKit.Editor/1.0.0": {"type": "project", "path": "UIKit.Editor.csproj", "msbuildProject": "UIKit.Editor.csproj"}, "Unity.Logging/1.0.0": {"type": "project", "path": "Unity.Logging.csproj", "msbuildProject": "Unity.Logging.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "AudioKit >= 1.0.0", "BestHTTP >= 1.0.0", "GifForUnity >= 1.0.0", "GifImporter.Editor >= 1.0.0", "MPUIKit >= 1.0.0", "MPUIKit.Editor >= 1.0.0", "Mochineko.VoiceActivityDetection >= 1.0.0", "Mochineko.VoiceActivityDetection.Components >= 1.0.0", "Mochineko.VoiceActivityDetection.Samples >= 1.0.0", "Paroxe.PDFRenderer >= 1.0.0", "Paroxe.PDFRenderer.Editor >= 1.0.0", "PrimeTween.Installer >= 1.0.0", "QFramework >= 1.0.0", "QFramework.CoreKit >= 1.0.0", "ResKit >= 1.0.0", "ResKit.Editor >= 1.0.0", "SupportOldQF >= 1.0.0", "UIKit >= 1.0.0", "UIKit.Editor >= 1.0.0", "Unity.Logging >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assembly-CSharp.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\AudioKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\BestHTTP.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifForUnity.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\GifImporter.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Components.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Mochineko.VoiceActivityDetection.Samples.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\MPUIKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Paroxe.PDFRenderer.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\PrimeTween.Installer.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.CoreKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\QFramework.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\ResKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\SupportOldQF.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\UIKit.Editor.csproj"}, "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj": {"projectPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Unity.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}