﻿using System;
using QFramework;
using UnityEngine;
using LitJson;
using XM.Common;

namespace XM.Core 
{
    /// <summary>
    /// 初始化AIChat场景命令
    /// </summary>
    public class ChatInitAIChatSceneCommand : AbstractCommand 
    {
        protected override void OnExecute()
        {
            this.GetSystem<AIChatSystem>().InitSystem();
            UIKit.OpenPanel<AIChatUIChatPanel>();
        }
    }

    /// <summary>
    /// 释放AIChat场景命令
    /// </summary>
    public class ChatDisposeAIChatSceneCommand : AbstractCommand 
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatDisposeAIChatSceneCommand");
            this.GetModel<ChatModel>().ClearChatModel();
            this.GetSystem<AIChatSystem>().ClearDataInfo();
            UIKit.CloseAllPanel();
        }
    }

    /// <summary>
    /// 获取Token指令
    /// </summary>
    public class ChatGetAccessTokenCommand : AbstractCommand
    {
        private string access_key = "1590b24225a145afaeaa761c712580f0";
        private string secret_key = "334a27cd53694a84ac317ae80700a904";
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatGetAccessTokenCommand");
            GetAccessToken();
        }


        private void GetAccessToken()
        {
            // 构建请求URL with query parameters
            string requestUrl;
            if (AIArchitecture.Interface.GetModel<UserModel>().UserData != null)
            {
                requestUrl = $"{URLData.URLRootPath}{URLData.URLGetClientCredentialsAccess_tokenPath}?appId={AIArchitecture.Interface.GetModel<UserModel>().UserData.appKey}&secret={AIArchitecture.Interface.GetModel<UserModel>().UserData.appSecret}&grant_type=client_credentials";
            }
            else
            {
                requestUrl = $"{URLData.URLRootPath}{URLData.URLGetClientCredentialsAccess_tokenPath}?appId={access_key}&secret={secret_key}&grant_type=client_credentials";
            }

            XMDebug.Log(this,$"GetAccessToken requestUrl: {requestUrl}");
            HttpRequestManager.Instance.Get(requestUrl, (response) => 
            {
                if (response != null)
                {
                    TokenResponse res = JsonMapper.ToObject<TokenResponse>(response);

                    if (res != null && res.code == 200 && res.success)
                    {
                        this.GetModel<ChatModel>().AccessTokenResponse = res;
                        TypeEventSystem.Global.Send(new GetAccessTokenDoneEvent() { isSuc = true});

                        XMDebug.Log(this,$"Access Token: {res.data.accessToken}");
                    }
                    else
                    {
                        XMDebug.LogError(this,$"Request failed. Code: {res?.code}, Message: {res?.msg}");
                        TypeEventSystem.Global.Send(new GetAccessTokenDoneEvent() { isSuc = false, msg = res.msg });
                    }
                }
                else
                {
                    XMDebug.LogError(this,$"Request failed.response Null");
                    TypeEventSystem.Global.Send(new GetAccessTokenDoneEvent() { isSuc = false ,msg = "response Null" });
                }
            });
        }
    }

    /// <summary>
    /// 创建会话指令
    /// </summary>
    public class ChatCreateConversationCommand : AbstractCommand
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatCreateConversationCommand");
            this.GetSystem<AIChatSystem>().StartNewConversation("测试对话");
        }
    }


    /// <summary>
    /// 检测声音是否有人说话
    /// </summary>
    public class ChatCheckAudioIsHumanTalkCommand : AbstractCommand 
    {
        private AudioClip _audioClip;
        public ChatCheckAudioIsHumanTalkCommand(AudioClip audioClip) 
        {
            _audioClip = audioClip;
        }
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatCheckAudioIsHumanTalkCommand _audioClip {_audioClip}");
            ///发送语音关闭录音【避免多录音，检测完毕后在继续录】
            //this.GetSystem<AIChatSystem>()._vad.SetIsCheck(false);
            this.GetModel<ChatModel>().UserTalkState.Value = UserTalkStateEnum.Done;
            ChatCheckAudioIsHumanTalk(_audioClip);
        }

        public void ChatCheckAudioIsHumanTalk(AudioClip _audioClip)
        {
            this.GetUtility<STTUtility>().SpeechToText(_audioClip, OnSpeechToTextInfoEvent, OnSpeechToTextErrorInfoEvent);
        }
        private void OnSpeechToTextInfoEvent(string textInfo, AudioClip _talkClip)
        {
            XMDebug.Log(this, $"OnSpeechToTextInfoEvent {textInfo}");
            this.GetModel<ChatModel>().UserTalkState.Value = UserTalkStateEnum.Convert;
            if (!string.IsNullOrEmpty(textInfo))
            {
                XMDebug.Log(this, $"OnSpeechToTextInfoEvent Is Human Voice");

                TypeEventSystem.Global.Send(new ChatCheckAudioIsHumanResultEvent() { isHuman = true, info = textInfo, checkAudioClip = _talkClip });
            }
            else
            {
                XMDebug.Log(this, $"OnSpeechToTextInfoEvent Is Not Human Voice");

                TypeEventSystem.Global.Send(new ChatCheckAudioIsHumanResultEvent() { isHuman = false, info = textInfo, checkAudioClip = _talkClip });
            }
        }
        private void OnSpeechToTextErrorInfoEvent(string errorInfo)
        {
            XMDebug.Log(this, $"OnSpeechToTextErrorInfoEvent errorInfo {errorInfo}");
            TypeEventSystem.Global.Send(new AIAgentErrorEvent() { errorInfo = errorInfo });
        }
    }

    /// <summary>
    /// 智能体说话完毕命令
    /// </summary>
    public class ChatAIAgentTalkDoneCommand : AbstractCommand 
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, "ChatAIAgentTalkDoneCommand");
            if (!this.GetModel<ChatModel>().IsReplyText.Value)
            {
                this.GetModel<ChatModel>().IsAIAgentTalk.Value = false;
                this.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Sleep;
                this.GetModel<ChatModel>().MicState.Value = MicStateEnum.StartCheck;
                this.GetSystem<AIChatSystem>().ClearDataInfo();
                TypeEventSystem.Global.Send(new AIChatAddHistoryInfoEvent() { isUser = false, info = this.GetModel<ChatModel>().AIAgentTalkInfo.Value });
            }
        }
    }

    /// <summary>
    /// 智能体文本说话完毕
    /// </summary>
    public class ChatAIAgentTextTalkDoneCommand : AbstractCommand 
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, "ChatAIAgentTextTalkDoneCommand");
            if (this.GetModel<ChatModel>().IsReplyText.Value)
            {
                this.GetModel<ChatModel>().IsAIAgentTalk.Value = false;
                this.GetModel<ChatModel>().AIAgentState.Value = AIAgentStateEnum.Sleep;
                this.GetModel<ChatModel>().MicState.Value = MicStateEnum.StartCheck;
                this.GetSystem<AIChatSystem>().ClearDataInfo();
                TypeEventSystem.Global.Send(new AIChatAddHistoryInfoEvent() { isUser = false, info = this.GetModel<ChatModel>().AIAgentTalkInfo.Value });
            }
        }
    }


    /// <summary>
    /// 检测声音是否是唤醒词
    /// </summary>
    public class ChatCheckAudioIsWakeWordCommand : AbstractCommand 
    {
        public AudioClip _checkAudio;
        public string _checkAudioTalkInfo;
        public ChatCheckAudioIsWakeWordCommand(AudioClip audio,string checkAudioTalkInfo) 
        {
            _checkAudio = audio;
            _checkAudioTalkInfo = checkAudioTalkInfo;
        }
        protected override void OnExecute()
        {
            XMDebug.Log(this, "ChatCheckAudioIsWakeWordCommand");
            ///关闭录音
            //this.GetSystem<AIChatSystem>()._vad.SetIsCheck(false);
            if (this.GetModel<ChatModel>().AIAgentState.Value == AIAgentStateEnum.Sleep)
            {
                ///如果在休眠状态则检测唤醒词
                this.GetSystem<AIChatSystem>().ChatCheckAudioIsWakeWord(_checkAudio, _checkAudioTalkInfo);
            }
            else
            {
                ///否则不需要进行唤醒词检测
                this.SendCommand(new ChatUserTalkWakeWordCommand(false, _checkAudioTalkInfo));
            }
        }
    }


    /// <summary>
    /// 发送消息给智能体指令
    /// </summary>
    public class ChatSendMessageToAgentCommand : AbstractCommand
    {
        public string _talkInfo;
        public ChatSendMessageToAgentCommand(string talkInfo) 
        {
            _talkInfo = talkInfo;
        }
        protected override void OnExecute()
        {
            XMDebug.Log(this, "ChatSendMessageToAgentCommand");
            this.GetSystem<AIChatSystem>().SendMessageToAgent(_talkInfo, this.GetModel<ChatModel>().CurrentConversationId);
            this.GetModel<ChatModel>().UserTalkInfo.Value = _talkInfo;
            this.GetModel<ChatModel>().AIAgentTalkInfo.Value = "";
            this.GetModel<ChatModel>().IsAIAgentTalk.Value = true;
            this.GetModel<ChatModel>().MicState.Value = MicStateEnum.StopCheck;
            //this.GetSystem<AIChatSystem>()._vad.SetIsCheck(false);
            TypeEventSystem.Global.Send(new AIChatAddHistoryInfoEvent() { isUser = true, info = _talkInfo });
        }
    }

    /// <summary>
    /// 重新获取检测人声音频指令
    /// </summary>
    public class ChatRestartGetCheckHumanVoiceCommand : AbstractCommand 
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, "ChatRestartGetCheckHumanVoiceCommand");
            ///重新开启录入
            this.GetSystem<AIChatSystem>()._vad.SetIsCheck(true);
            this.GetModel<ChatModel>().MicState.Value = MicStateEnum.NoHumanVoice;
        }
    }


    /// <summary>
    /// 展示本地对话框【不进行智能体对话操作】
    /// </summary>
    public class ChatShowLocalTalkFrameCommand : AbstractCommand 
    {
        private bool isUserTalk = false;
        private string talkInfo;
        public ChatShowLocalTalkFrameCommand(bool _isUserTalk,string _talkInfo) 
        {
            isUserTalk = _isUserTalk;
            talkInfo = _talkInfo;
        }
        protected override void OnExecute()
        {
            if (isUserTalk)
            {
                UIKit.GetPanel<AIChatUIChatPanel>().ChatShowUserTalk(talkInfo);
            }
            else
            {
                UIKit.GetPanel<AIChatUIChatPanel>().ChatShowAIAgentTalk(talkInfo);
            }
        }
    }
    /// <summary>
    /// VAD检测模块是否激活结果指令
    /// </summary>
    public class ChatVADIsActiveResultCommand : AbstractCommand
    {
        private bool _isActive;
        public ChatVADIsActiveResultCommand(bool isActive) 
        {
            _isActive = isActive;
        }
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatRestartGetCheckHumanVoiceCommand _isActive {_isActive}");
            if (_isActive)
                this.GetModel<ChatModel>().UserTalkState.Value = UserTalkStateEnum.Recoding;
            this.GetModel<ChatModel>().MicState.Value = _isActive ? MicStateEnum.StartRecord : MicStateEnum.StopCheck;
        }
    }

    /// <summary>
    /// 改变语音还是文本显示状态命令
    /// </summary>
    public class ChatChangeReplyTextStateCommand : AbstractCommand 
    {
        private bool _isReplyText;
        public ChatChangeReplyTextStateCommand(bool isReplyText)
        {
            _isReplyText = isReplyText;
        }
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"ChatChangeReplyTextStateCommand _isReplyText {_isReplyText}");
            this.GetModel<ChatModel>().IsReplyText.Value = _isReplyText;
        }
    }


    /// <summary>
    /// 更新AI智能体
    /// </summary>
    public class ChatUpdateAIAgentCommand : AbstractCommand 
    {
        public LoginARGlassAIAgentItemData _aiAgentData;
        public ChatUpdateAIAgentCommand(LoginARGlassAIAgentItemData aiAgentData)
        {
            _aiAgentData = aiAgentData;
        }
        protected override void OnExecute()
        {
            AIArchitecture.Interface.GetModel<UserModel>().CurrentAIAgentCode = _aiAgentData.code;

            this.GetSystem<AIChatSystem>().ClearDataInfo();
            UIKit.GetPanel<AIChatUIAIAgentChoicePanel>().ShowLoadingAIAgent();
            ///重新创建对话
            this.SendCommand<ChatCreateConversationCommand>();
        }
    }


    /// <summary>
    /// 用户说唤醒词命令
    /// </summary>
    public class ChatUserTalkWakeWordCommand : AbstractCommand 
    {
        private bool isWakeWord;
        private string talkInfo;
        public ChatUserTalkWakeWordCommand(bool _isWakeWord,string _talkInfo) 
        {
            isWakeWord = _isWakeWord;
            talkInfo = _talkInfo;
        }
        protected override void OnExecute()
        {
            this.GetSystem<AIChatSystem>().UserTalkWakeWordAudioDone(isWakeWord, talkInfo);
        }
    }

    /// <summary>
    /// /智能体状态改变命令
    /// </summary>
    public class ChatAIAgentStateChangeCommand : AbstractCommand 
    {
        private AIAgentStateEnum _state;
        public ChatAIAgentStateChangeCommand(AIAgentStateEnum state) 
        {
            _state = state;
        }
        protected override void OnExecute()
        {
            this.GetSystem<AIChatSystem>().AIAgentStateChange(_state);  
        }
    }

    public class OnBackCommand:AbstractCommand 
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"OnBackEvent Back Login Scene");
            ///如果选择Panel页面打开，则关闭页面
            if (UIKit.GetPanel<AIChatUIAIAgentChoicePanel>() != null && UIKit.GetPanel<AIChatUIAIAgentChoicePanel>().State == PanelState.Opening)
            {
                this.GetSystem<AIChatSystem>()._vad.SetIsCheck(true);
                UIKit.ClosePanel<AIChatUIAIAgentChoicePanel>();
            }
            else
            {
                ///选择页面关闭则加载场景
                if (this.GetUtility<SceneUtility>().GetCurrentScene() == SceneType.AIChat)
                {
                    this.SendCommand<ChatDisposeAIChatSceneCommand>();
                    this.GetUtility<SceneUtility>().LoadScene(SceneType.Login);
                }
            }
        }
    }
    public class ChatTripleTapEvent : AbstractCommand
    {
        protected override void OnExecute()
        {
            XMDebug.Log(this, $"OnTripleTapRayneoEvent OpenChoiceAIAgentPage");

            if (this.GetModel<ChatModel>().IsAIAgentTalk.Value)
            {
                TipsPanelUIData tipsPanelUIData = new TipsPanelUIData() { TipsInfo = "需要等待智能体说话完毕才可以切换智能体" };
                TipsPanel tipsPanel = UIKit.GetPanel<TipsPanel>();
                if (tipsPanel == null)
                {
                    UIKit.OpenPanel<TipsPanel>(PanelOpenType.Single, UILevel.PopUI).ShowTipsInfo(tipsPanelUIData);
                }
            }
            else
            {
                this.GetSystem<AIChatSystem>()._vad.SetIsCheck(false);
                UIKit.OpenPanel<AIChatUIAIAgentChoicePanel>(new AIChatUIAIAgentChoicePanelUIData()
                {
                    itemList = AIArchitecture.Interface.GetModel<UserModel>().UserData.GetUserIdentityInfoToOrgCode(false, AIArchitecture.Interface.GetModel<UserModel>().CurrentSelectUserIdentityOrgCode)
                .GetLoginARGlassAIAgentItemDataToAppId(AIArchitecture.Interface.GetModel<UserModel>().CurrentApplicationId)
                });
            }
        }
    }
}