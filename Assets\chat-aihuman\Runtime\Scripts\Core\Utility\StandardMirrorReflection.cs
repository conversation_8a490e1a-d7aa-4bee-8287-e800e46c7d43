using UnityEngine;
using System.Collections.Generic;

namespace XM.Core
{
    /// <summary>
    /// 标准渲染管线镜面反射工具类
    /// 专门为Built-in Render Pipeline优化
    /// </summary>
    public class StandardMirrorReflection : MonoBehaviour
    {
        [Header("反射设置")]
        [SerializeField] private LayerMask reflectionLayers = -1;
        [SerializeField] private bool disablePixelLights = true;
        [SerializeField] private int textureSize = 256;
        [SerializeField] private float clipPlaneOffset = 0.07f;
        
        [Header("性能优化")]
        [SerializeField] private bool useOcclusionCulling = true;
        [SerializeField] private float reflectionDistance = 100f;
        [SerializeField] private int maxReflectionFPS = 30;
        [SerializeField] private bool enableMobileOptimization = true;
        
        [Header("质量设置")]
        [SerializeField] private ReflectionQuality quality = ReflectionQuality.Medium;
        
        [Header("标准管线特定")]
        [SerializeField] private bool enableFog = true;
        [SerializeField] private bool enableSkybox = true;
        [SerializeField] private RenderingPath renderingPath = RenderingPath.Forward;
        
        public enum ReflectionQuality
        {
            Low = 128,
            Medium = 256,
            High = 512,
            Ultra = 1024
        }
        
        private Dictionary<Camera, Camera> reflectionCameras = new Dictionary<Camera, Camera>();
        private RenderTexture reflectionTexture;
        private Material mirrorMaterial;
        private float lastReflectionTime;
        private float reflectionInterval;
        
        private static bool isReflecting = false; // 防止递归渲染
        
        private void Start()
        {
            InitializeReflection();
        }
        
        private void InitializeReflection()
        {
            // 根据质量设置调整纹理大小
            textureSize = (int)quality;
            
            // 移动端优化
            if (enableMobileOptimization)
            {
                ApplyMobileOptimizations();
            }
            
            // 计算反射更新间隔
            reflectionInterval = 1f / maxReflectionFPS;
            
            // 获取材质
            var renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                mirrorMaterial = renderer.material;
            }
            
            CreateReflectionTexture();
        }
        
        private void ApplyMobileOptimizations()
        {
            // 移动端降低质量
            if (SystemInfo.systemMemorySize < 3000) // 3GB以下设备
            {
                textureSize = Mathf.Min(textureSize, 256);
                maxReflectionFPS = 20;
                reflectionDistance = 50f;
                disablePixelLights = true;
                renderingPath = RenderingPath.VertexLit;
            }
            
            // 低端设备进一步优化
            if (SystemInfo.systemMemorySize < 2000) // 2GB以下设备
            {
                textureSize = 128;
                maxReflectionFPS = 15;
                reflectionDistance = 30f;
                enableFog = false;
            }
        }
        
        private void CreateReflectionTexture()
        {
            if (reflectionTexture != null)
            {
                DestroyImmediate(reflectionTexture);
            }
            
            reflectionTexture = new RenderTexture(textureSize, textureSize, 16)
            {
                name = "StandardMirrorReflection_" + GetInstanceID(),
                isPowerOfTwo = true,
                hideFlags = HideFlags.DontSave,
                filterMode = FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Clamp
            };
            
            // 移动端优化格式
            if (enableMobileOptimization)
            {
                reflectionTexture.format = RenderTextureFormat.RGB565;
            }
            else
            {
                reflectionTexture.format = RenderTextureFormat.ARGB32;
            }
            
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetTexture("_ReflectionTex", reflectionTexture);
            }
        }
        
        private void OnWillRenderObject()
        {
            if (!enabled || !GetComponent<Renderer>() || !GetComponent<Renderer>().enabled)
                return;
                
            Camera cam = Camera.current;
            if (!cam || isReflecting)
                return;
                
            // 帧率限制
            if (Time.time - lastReflectionTime < reflectionInterval)
                return;
                
            lastReflectionTime = Time.time;
            
            isReflecting = true;
            
            Camera reflectionCamera;
            CreateReflectionCameraFor(cam, out reflectionCamera);
            
            // 计算反射矩阵
            Vector3 pos = transform.position;
            Vector3 normal = transform.up;
            
            UpdateCameraModes(cam, reflectionCamera);
            
            // 反射矩阵计算
            float d = -Vector3.Dot(normal, pos) - clipPlaneOffset;
            Vector4 reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
            
            Matrix4x4 reflection = Matrix4x4.zero;
            CalculateReflectionMatrix(ref reflection, reflectionPlane);
            
            Vector3 oldpos = cam.transform.position;
            Vector3 newpos = reflection.MultiplyPoint(oldpos);
            reflectionCamera.worldToCameraMatrix = cam.worldToCameraMatrix * reflection;
            
            // 设置斜视锥体
            Vector4 clipPlane = CameraSpacePlane(reflectionCamera, pos, normal, 1.0f);
            reflectionCamera.projectionMatrix = cam.CalculateObliqueMatrix(clipPlane);
            
            reflectionCamera.cullingMask = ~(1 << 4) & reflectionLayers.value;
            reflectionCamera.targetTexture = reflectionTexture;
            
            // 标准管线特定设置
            reflectionCamera.renderingPath = renderingPath;
            reflectionCamera.useOcclusionCulling = useOcclusionCulling;
            
            // 移动端优化设置
            if (enableMobileOptimization)
            {
                reflectionCamera.renderingPath = RenderingPath.Forward;
                reflectionCamera.allowHDR = false;
                reflectionCamera.allowMSAA = false;
            }
            
            // 禁用像素光源以提高性能
            if (disablePixelLights)
            {
                int oldPixelLightCount = QualitySettings.pixelLightCount;
                QualitySettings.pixelLightCount = 0;
                
                GL.invertCulling = true;
                reflectionCamera.transform.position = newpos;
                Vector3 euler = cam.transform.eulerAngles;
                reflectionCamera.transform.eulerAngles = new Vector3(-euler.x, euler.y, euler.z);
                reflectionCamera.Render();
                reflectionCamera.transform.position = oldpos;
                GL.invertCulling = false;
                
                QualitySettings.pixelLightCount = oldPixelLightCount;
            }
            else
            {
                GL.invertCulling = true;
                reflectionCamera.transform.position = newpos;
                Vector3 euler = cam.transform.eulerAngles;
                reflectionCamera.transform.eulerAngles = new Vector3(-euler.x, euler.y, euler.z);
                reflectionCamera.Render();
                reflectionCamera.transform.position = oldpos;
                GL.invertCulling = false;
            }
            
            isReflecting = false;
        }
        
        private void CreateReflectionCameraFor(Camera cam, out Camera reflectionCamera)
        {
            reflectionCamera = null;
            
            if (!reflectionCameras.TryGetValue(cam, out reflectionCamera))
            {
                GameObject go = new GameObject("Standard Mirror Reflection Camera id" + GetInstanceID() + " for " + cam.GetInstanceID(), typeof(Camera));
                reflectionCamera = go.GetComponent<Camera>();
                reflectionCamera.enabled = false;
                reflectionCamera.transform.position = transform.position;
                reflectionCamera.transform.rotation = transform.rotation;
                
                // 标准管线特定组件
                if (enableSkybox)
                {
                    go.AddComponent<Skybox>();
                }
                go.AddComponent<FlareLayer>();
                
                go.hideFlags = HideFlags.HideAndDontSave;
                reflectionCameras[cam] = reflectionCamera;
            }
        }
        
        private void UpdateCameraModes(Camera src, Camera dest)
        {
            if (dest == null)
                return;
                
            dest.clearFlags = src.clearFlags;
            dest.backgroundColor = src.backgroundColor;
            
            // 天空盒设置
            if (enableSkybox && src.clearFlags == CameraClearFlags.Skybox)
            {
                Skybox sky = src.GetComponent<Skybox>();
                Skybox mysky = dest.GetComponent<Skybox>();
                if (!sky || !sky.material)
                {
                    if (mysky) mysky.enabled = false;
                }
                else
                {
                    if (!mysky) mysky = dest.gameObject.AddComponent<Skybox>();
                    mysky.enabled = true;
                    mysky.material = sky.material;
                }
            }
            
            dest.farClipPlane = Mathf.Min(src.farClipPlane, reflectionDistance);
            dest.nearClipPlane = src.nearClipPlane;
            dest.orthographic = src.orthographic;
            dest.fieldOfView = src.fieldOfView;
            dest.aspect = src.aspect;
            dest.orthographicSize = src.orthographicSize;
            
            // 雾效设置
            dest.renderingPath = renderingPath;
            
            // 标准管线特定设置
            dest.depthTextureMode = DepthTextureMode.None; // 移动端优化
        }
        
        private static void CalculateReflectionMatrix(ref Matrix4x4 reflectionMat, Vector4 plane)
        {
            reflectionMat.m00 = (1F - 2F * plane[0] * plane[0]);
            reflectionMat.m01 = (-2F * plane[0] * plane[1]);
            reflectionMat.m02 = (-2F * plane[0] * plane[2]);
            reflectionMat.m03 = (-2F * plane[3] * plane[0]);
            
            reflectionMat.m10 = (-2F * plane[1] * plane[0]);
            reflectionMat.m11 = (1F - 2F * plane[1] * plane[1]);
            reflectionMat.m12 = (-2F * plane[1] * plane[2]);
            reflectionMat.m13 = (-2F * plane[3] * plane[1]);
            
            reflectionMat.m20 = (-2F * plane[2] * plane[0]);
            reflectionMat.m21 = (-2F * plane[2] * plane[1]);
            reflectionMat.m22 = (1F - 2F * plane[2] * plane[2]);
            reflectionMat.m23 = (-2F * plane[3] * plane[2]);
            
            reflectionMat.m30 = 0F;
            reflectionMat.m31 = 0F;
            reflectionMat.m32 = 0F;
            reflectionMat.m33 = 1F;
        }
        
        private Vector4 CameraSpacePlane(Camera cam, Vector3 pos, Vector3 normal, float sideSign)
        {
            Vector3 offsetPos = pos + normal * clipPlaneOffset;
            Matrix4x4 m = cam.worldToCameraMatrix;
            Vector3 cpos = m.MultiplyPoint(offsetPos);
            Vector3 cnormal = m.MultiplyVector(normal).normalized * sideSign;
            return new Vector4(cnormal.x, cnormal.y, cnormal.z, -Vector3.Dot(cpos, cnormal));
        }
        
        private void OnDisable()
        {
            if (reflectionTexture)
            {
                DestroyImmediate(reflectionTexture);
                reflectionTexture = null;
            }
            
            foreach (var kvp in reflectionCameras)
            {
                if (kvp.Value)
                    DestroyImmediate(kvp.Value.gameObject);
            }
            reflectionCameras.Clear();
        }
        
        /// <summary>
        /// 运行时调整反射质量
        /// </summary>
        public void SetReflectionQuality(ReflectionQuality newQuality)
        {
            quality = newQuality;
            textureSize = (int)quality;
            CreateReflectionTexture();
        }
        
        /// <summary>
        /// 设置反射强度
        /// </summary>
        public void SetReflectionIntensity(float intensity)
        {
            if (mirrorMaterial != null)
            {
                mirrorMaterial.SetFloat("_ReflectionIntensity", intensity);
            }
        }
        
        /// <summary>
        /// 启用/禁用反射
        /// </summary>
        public void EnableReflection(bool enable)
        {
            if (mirrorMaterial != null)
            {
                if (enable)
                    mirrorMaterial.EnableKeyword("_ENABLEREFLECTION_ON");
                else
                    mirrorMaterial.DisableKeyword("_ENABLEREFLECTION_ON");
            }
        }
        
        /// <summary>
        /// 设置渲染路径
        /// </summary>
        public void SetRenderingPath(RenderingPath path)
        {
            renderingPath = path;
        }
    }
}
