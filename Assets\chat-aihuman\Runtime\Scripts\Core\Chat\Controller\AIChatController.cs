﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using MoonSharp.VsCodeDebugger.SDK;
using QFramework;
using UnityEngine;
using XM.Common;
using XM.Core;


namespace XM.Core
{
    public class AIChatControllerD : <PERSON>o<PERSON><PERSON><PERSON>our,IController
    {
        void Start() 
        {
            this.SendCommand<ChatInitAIChatSceneCommand>();
            this.SendCommand<ChatGetAccessTokenCommand>();
        }
        void Update()
        {
            this.GetSystem<AIChatSystem>().OnUpdate();
        }

        private void OnEnable()
        {
            Register();
        }

        private void OnDisable()
        {
            UnRegister();
        }


        private void Register()
        {
            TypeEventSystem.Global.Register<VADRecordClipEvent>(OnVADRecordClipEvent);
            //TypeEventSystem.Global.Register<AIAgentTalkDoneEvent>(OnAIAgentTalkDoneEvent);
            TypeEventSystem.Global.Register<VADIsActiveEvent>(OnVADIsActiveEvent);
            TypeEventSystem.Global.Register<AIAgentTextDoneEvent>(OnAIAgentTextDoneEvent);
            TypeEventSystem.Global.Register<AIAgentErrorEvent>(OnAIAgentErrorEventEvent);
            TypeEventSystem.Global.Register<ClickReplyTextButtonEvent>(OnClickReplyTextButtonEvent);
            TypeEventSystem.Global.Register<ClickReplyVoiceButtonEvent>(OnClickReplyVoiceButtonEvent);

            TypeEventSystem.Global.Register<LoginARGlassClickAIAgentEvent>(OnLoginARGlassClickAIAgentEvent);
            

            TypeEventSystem.Global.Register<GetAccessTokenDoneEvent>(OnGetAccessTokenDoneEvent);
            TypeEventSystem.Global.Register<StartNewConversationResultEvent>(OnStartNewConversationResultEvent);
            TypeEventSystem.Global.Register<ChatCheckAudioIsHumanResultEvent>(OnChatCheckAudioIsHumanResultEvent);
            TypeEventSystem.Global.Register<ChatShowUserTalkEvent>(OnChatShowUserTalkEvent);
            TypeEventSystem.Global.Register<ChatShowAIAgentTalkEvent>(OnChatShowAIAgentTalkEvent);
            TypeEventSystem.Global.Register<ChatCheckWakeWordResultEvent>(OnChatCheckWakeWordResultEvent);

            this.GetModel<ChatModel>().AIAgentState.Register(OnAIAgentStateChangeEvent);

            TypeEventSystem.Global.Register<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);

            InputManager.Instance.OnBack += OnBackEvent;
            InputManager.Instance.OnTripleTap += OnOnTripleTapEvent;
        }



        private void UnRegister()
        {
            TypeEventSystem.Global.UnRegister<VADRecordClipEvent>(OnVADRecordClipEvent);
            //TypeEventSystem.Global.UnRegister<AIAgentTalkDoneEvent>(OnAIAgentTalkDoneEvent);
            TypeEventSystem.Global.UnRegister<VADIsActiveEvent>(OnVADIsActiveEvent);
            TypeEventSystem.Global.UnRegister<AIAgentTextDoneEvent>(OnAIAgentTextDoneEvent);
            TypeEventSystem.Global.UnRegister<AIAgentErrorEvent>(OnAIAgentErrorEventEvent);
            TypeEventSystem.Global.UnRegister<ClickReplyTextButtonEvent>(OnClickReplyTextButtonEvent);
            TypeEventSystem.Global.UnRegister<ClickReplyVoiceButtonEvent>(OnClickReplyVoiceButtonEvent);
            TypeEventSystem.Global.UnRegister<LoginARGlassClickAIAgentEvent>(OnLoginARGlassClickAIAgentEvent);

            TypeEventSystem.Global.UnRegister<GetAccessTokenDoneEvent>(OnGetAccessTokenDoneEvent);
            TypeEventSystem.Global.UnRegister<StartNewConversationResultEvent>(OnStartNewConversationResultEvent);
            TypeEventSystem.Global.UnRegister<ChatCheckAudioIsHumanResultEvent>(OnChatCheckAudioIsHumanResultEvent);
            TypeEventSystem.Global.UnRegister<ChatShowUserTalkEvent>(OnChatShowUserTalkEvent);
            TypeEventSystem.Global.UnRegister<ChatShowAIAgentTalkEvent>(OnChatShowAIAgentTalkEvent);
            TypeEventSystem.Global.UnRegister<ChatCheckWakeWordResultEvent>(OnChatCheckWakeWordResultEvent);

            this.GetModel<ChatModel>().AIAgentState.UnRegister(OnAIAgentStateChangeEvent);

            TypeEventSystem.Global.UnRegister<DoubleTapRayneoEvent>(OnDoubleTapRayneoEvent);

            InputManager.Instance.OnBack -= OnBackEvent;
            InputManager.Instance.OnTripleTap -= OnOnTripleTapEvent;
        }


        private void OnVADRecordClipEvent(VADRecordClipEvent @event)
        {
            XMDebug.Log(this, $"OnVADRecordClipEvent");
            this.SendCommand(new ChatCheckAudioIsHumanTalkCommand(@event._clip));
        }
        private void OnAIAgentTalkDoneEvent(AIAgentTalkDoneEvent @event)
        {
            XMDebug.Log(this, $"OnAIAgentTalkDoneEvent");
            //测试暂时注释掉
            //this.SendCommand<ChatAIAgentTalkDoneCommand>();
        }
        private void OnVADIsActiveEvent(VADIsActiveEvent @event)
        {
            XMDebug.Log(this, $"OnVADIsActiveEvent isActive {@event.isActive}");
            this.SendCommand(new ChatVADIsActiveResultCommand(@event.isActive));
        }
        private void OnAIAgentTextDoneEvent(AIAgentTextDoneEvent @event)
        {
            XMDebug.Log(this, $"OnAIAgentTextDoneEvent");
            this.SendCommand<ChatAIAgentTextTalkDoneCommand>();
        }

        private void OnAIAgentErrorEventEvent(AIAgentErrorEvent @event)
        {
            XMDebug.Log(this, $"OnAIAgentErrorEventEvent errorInfo {@event.errorInfo}");
            UIKit.OpenPanel<AIChatUIErrorPanel>(new AIChatUIErrorPanellUIData() { errorInfo = @event.errorInfo });
        }
        private void OnClickReplyTextButtonEvent(ClickReplyTextButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickReplyTextButtonEvent");
            this.SendCommand(new ChatChangeReplyTextStateCommand(false));
        }

        private void OnClickReplyVoiceButtonEvent(ClickReplyVoiceButtonEvent @event)
        {
            XMDebug.Log(this, $"OnClickReplyVoiceButtonEvent");
            this.SendCommand(new ChatChangeReplyTextStateCommand(true));
        }

        private void OnLoginARGlassClickAIAgentEvent(LoginARGlassClickAIAgentEvent @event)
        {
            XMDebug.Log(this, $"OnLoginARGlassClickAIAgentEvent");
            this.SendCommand(new ChatUpdateAIAgentCommand(@event.aiAgent));
        }
        private void OnGetAccessTokenDoneEvent(GetAccessTokenDoneEvent @event)
        {
            if (@event.isSuc)
            {
                XMDebug.Log(this, $"OnGetAccessTokenDoneEvent Success");
                this.SendCommand<ChatCreateConversationCommand>();
            }
            else
            {
                XMDebug.LogError(this, $"OnGetAccessTokenDoneEvent error {@event.msg}");
                UIKit.OpenPanel<AIChatUIErrorPanel>(new AIChatUIErrorPanellUIData() { errorInfo = @event.msg });
            }
        }

        private void OnStartNewConversationResultEvent(StartNewConversationResultEvent @event)
        {
            if (@event.isSuc)
            {
                XMDebug.Log(this, $"OnStartNewConversationResultEvent Success");
                if (UIKit.GetPanel<AIChatUIAIAgentChoicePanel>() != null && UIKit.GetPanel<AIChatUIAIAgentChoicePanel>().State == PanelState.Opening)
                {
                    this.SendCommand<OnBackCommand>();
                }
                this.GetModel<ChatModel>().CurrentConversationId = @event.conversationId;
            }
            else
            {
                XMDebug.LogError(this, $"OnStartNewConversationResultEvent error {@event.msg}");
                UIKit.OpenPanel<AIChatUIErrorPanel>(new AIChatUIErrorPanellUIData() { errorInfo = @event.msg });
            }
        }

        private void OnChatCheckAudioIsHumanResultEvent(ChatCheckAudioIsHumanResultEvent @event)
        {
            if (@event.isHuman)
            {
                XMDebug.Log(this, $"OnChatCheckAudioIsHumanResultEvent IsHuman info {@event.info}");
                this.SendCommand(new ChatCheckAudioIsWakeWordCommand(@event.checkAudioClip,@event.info));
            }
            else
            {
                XMDebug.Log(this, $"OnChatCheckAudioIsHumanResultEvent NoHuman");
                this.SendCommand<ChatRestartGetCheckHumanVoiceCommand>();
            }
        }

        private void OnChatShowUserTalkEvent(ChatShowUserTalkEvent @event)
        {
            XMDebug.Log(this, $"OnChatShowUserTalkEvent info {@event.talkInfo}");
            this.SendCommand(new ChatShowLocalTalkFrameCommand(true,@event.talkInfo));
        }

        private void OnChatShowAIAgentTalkEvent(ChatShowAIAgentTalkEvent @event)
        {
            XMDebug.Log(this, $"OnChatShowAIAgentTalkEvent info {@event.talkInfo}");
            this.SendCommand(new ChatShowLocalTalkFrameCommand(false, @event.talkInfo));
        }
        private void OnChatCheckWakeWordResultEvent(ChatCheckWakeWordResultEvent @event)
        {
            XMDebug.Log(this, $"OnChatCheckWakeWordResultEvent isWakeWord {@event.isWakeWord} talkInfo {@event.talkInfo}");
            this.SendCommand(new ChatUserTalkWakeWordCommand(@event.isWakeWord, @event.talkInfo));
        }
        private void OnAIAgentStateChangeEvent(AIAgentStateEnum @enum)
        {
            XMDebug.Log(this, $"OnAIAgentStateChangeEvent @enum {@enum}");
            this.SendCommand(new ChatAIAgentStateChangeCommand(@enum));
        }
        private void OnDoubleTapRayneoEvent(DoubleTapRayneoEvent @event)
        {
            XMDebug.Log(this, $"OnDoubleTapRayneoEvent");
            this.SendCommand<OnBackCommand>();
        }
        private void OnOnTripleTapEvent()
        {
            XMDebug.Log(this, $"OnTripleTapRayneoEvent");
            this.SendCommand<ChatTripleTapEvent>();
        }

        private void OnBackEvent()
        {
            XMDebug.Log(this, $"OnBackEvent");
            this.SendCommand<OnBackCommand>();
        }

        public IArchitecture GetArchitecture()
        {
            return AIArchitecture.Interface;
        }
    }
}
