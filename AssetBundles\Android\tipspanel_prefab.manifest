ManifestFileVersion: 0
CRC: 1791019793
Hashes:
  AssetFileHash:
    serializedVersion: 2
    Hash: 840eabf63b70cc9c3698952c01b4061c
  TypeTreeHash:
    serializedVersion: 2
    Hash: 26d9f75821a77fd344392cbba79b9f3c
  IncrementalBuildHash:
    serializedVersion: 2
    Hash: 840eabf63b70cc9c3698952c01b4061c
HashAppended: 0
ClassTypes:
- Class: 1
  Script: {instanceID: 0}
- Class: 21
  Script: {instanceID: 0}
- Class: 28
  Script: {instanceID: 0}
- Class: 48
  Script: {instanceID: 0}
- Class: 114
  Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 559e6ab670a7cd3499318ab22d567337, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
- Class: 114
  Script: {fileID: 11500000, guid: 141109fb25f0083469e71ce8cb3e75c8, type: 3}
- Class: 115
  Script: {instanceID: 0}
- Class: 128
  Script: {instanceID: 0}
- Class: 222
  Script: {instanceID: 0}
- Class: 224
  Script: {instanceID: 0}
- Class: 225
  Script: {instanceID: 0}
SerializeReferenceClassIdentifiers:
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Circle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.GradientEffect
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Hexagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.NStarPolygon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Pentagon
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Rectangle
- AssemblyName: MPUIKit
  ClassName: MPUIKIT.Triangle
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.Events.PersistentCallGroup
- AssemblyName: UnityEngine.CoreModule
  ClassName: UnityEngine.RectOffset
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.FontData
- AssemblyName: UnityEngine.UI
  ClassName: UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
Assets:
- Assets/chat-aihuman/Runtime/Prefab/UI/Common/TipsPanel.prefab
Dependencies: []
