{"PackageRepositories": [{"id": "c7ab022e-cc5b-4e18-9f8a-89ed2e8c15a8", "description": "", "name": "VersionCheckKit", "author": "liang<PERSON>e", "latestVersion": "v0.2.4", "latestDownloadUrl": "https://file.liangxiegame.com/VersionCheckKitv0_2_4_a92b8cad_6b08_4445_8ef8_e016b44edf6b.unitypackage", "installPath": "Assets/VersionCheckKit/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "72db6066-f805-4c8a-b40f-65b5cbf9c0c0", "description": "", "name": "UIWidgetsReduxPersist", "author": "liang<PERSON>e", "latestVersion": "v0.3.0", "latestDownloadUrl": "http://file.liangxiegame.com/UIWidgetsReduxPersistv0_3_0_e119c063_9098_435b_91f1_7e036a9d4acd.unitypackage", "installPath": "Assets/QFramework/UIWidgetsReduxPersist/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "0275c9da-c7ef-4f43-89b6-419bbd262a8c", "description": "", "name": "UIKitTransition", "author": "liang<PERSON>e", "latestVersion": "v0.0.1", "latestDownloadUrl": "http://file.liangxiegame.com/UIKitTransitionv0_0_1_a9c60351_695a_41ed_b6c4_8db806ee543a.unitypackage", "installPath": "Assets/QFramework/Extensions/UIKitTransition/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "524c1eed-260e-4192-9609-02900d82d7d6", "description": "", "name": "UIKitExtensions", "author": "liang<PERSON>e", "latestVersion": "v0.0.1", "latestDownloadUrl": "http://file.liangxiegame.com/UIKitExtensionsv0_0_1_a40e78a7_def8_4558_b375_ec5baf00c5d2.unitypackage", "installPath": "Assets/QFramework/UIKitExtensions/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "17f23180-a641-4060-948d-f0863d135a43", "description": "", "name": "UIKitExample_LoadFromResources", "author": "liang<PERSON>e", "latestVersion": "v0.0.0", "latestDownloadUrl": "empty", "installPath": "empty", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "9427a2b2-b118-49ae-991b-51d1f99ee279", "description": "", "name": "UIKitDemo_TodoList", "author": "liang<PERSON>e", "latestVersion": "v0.0.11", "latestDownloadUrl": "http://file.liangxiegame.com/UIKitDemo_TodoListv0_0_11_211f2c09_03fb_4ad4_8696_47fb6207c736.unitypackage", "installPath": "Assets/QFramework/UIKitDemo_TodoList/", "includeFileOrFolders": [], "accessRight": "public", "type": "Example/Demo", "isOfficial": false}, {"id": "41619e5b-630f-43da-9bd1-c526b83c7b9e", "description": "", "name": "UIImageWaveDirectionShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.7", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageWaveDirectionShaderv0_0_7_78ee6322_b5e1_4872_9e37_12f9c0abfb31.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageWaveDirectionShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "c354ebd7-d9b4-4e54-a5dc-969bb3bc674a", "description": "", "name": "UIImageWaveCircleShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageWaveCircleShaderv0_0_5_5cb9afb2_860c_4354_8d86_68bb77ab92b4.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageWaveCircleShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "62ab8268-4b1a-41ce-99c5-0a8a40fad137", "description": "", "name": "UIImageSpotLightShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.3", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageSpotLightShaderv0_0_3_bbb031a4_4059_44df_a300_218811decd98.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageSpotLightShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "1a6c997f-2960-4adc-a4e7-6f2133156b40", "description": "", "name": "UIImageSharpShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.3", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageSharpShaderv0_0_3_5232a557_1499_4463_8cbe_83bdd626b924.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageSharpShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "56f90411-9b5b-4922-83a1-2caf4dbb90bd", "description": "", "name": "UIImageRoundCornerShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageRoundCornerShaderv0_0_5_50ad2031_2b5a_4d7d_8f3f_5a7ccc6aa3ba.unitypackage", "installPath": "Assets/QFramework/Shaders/UIImageRoundCornerShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "5ae25b1d-6f72-4ffa-8d69-ef361736849d", "description": "", "name": "UIImage<PERSON><PERSON><PERSON><PERSON>", "author": "liang<PERSON>e", "latestVersion": "v0.0.3", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageReliefShaderv0_0_3_86c5a1b2_2623_41e9_bcf2_6d71bd079f30.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageReliefShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "c04cf0af-bcda-4830-a18a-b8a3108f468a", "description": "", "name": "UIImageReflectionShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageReflectionShaderv0_0_5_5372fb42_6ec9_4d9f_ac10_28ed5509f563.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageReflectionShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "0d708e46-31f6-4c3e-b0d7-076d1a04414c", "description": "", "name": "UIImageOutlineShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageOutlineShaderv0_0_4_026cce6a_c75f_4fac_a010_405f7b89d81f.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageOutlineShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "4a78b663-171d-46ad-aa40-67de92dc45ec", "description": "", "name": "UIImageOutlineAlphaShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageOutlineAlphaShaderv0_0_4_f01a088d_dcd4_4038_94fb_556e21b50a61.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageOutlineAlphaShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "1e4cd76b-70a3-4cdb-ae5f-5e4a0cbb0418", "description": "", "name": "UIImageGray<PERSON><PERSON><PERSON>", "author": "liang<PERSON>e", "latestVersion": "v0.0.8", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageGrayShaderv0_0_8_97236a77_a665_4002_a609_cf96c0ea66c5.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageGrayShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "d4f613aa-b9f7-4249-a783-5ecbd85be1db", "description": "", "name": "UIImageGradientShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageGradientShaderv0_0_4_3c529ad3_a597_4686_ae23_6803feebe174.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageGradientShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "051af663-c338-406f-a400-6d7e3a627cdc", "description": "", "name": "UIImageFlashLightShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageFlashLightShaderv0_0_5_06578e5d_47b9_4f62_b4d9_67028099a747.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageFlashLightShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "16971047-03d0-48a1-b9f3-3507eafcc002", "description": "", "name": "UIImageColorFilterShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.3", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageColorFilterShaderv0_0_3_b8ceb86e_5754_445b_8018_71654a0302ba.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageColorFilterShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "d7fc13b9-b59c-45b5-8db9-6f9b6ea20d03", "description": "", "name": "UIImageBumpsShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageBumpsShaderv0_0_5_d7626359_47d5_4add_be58_23ba0d09c089.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageBumpsShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "30063b6f-261b-4842-a328-3acdff11ffe6", "description": "", "name": "UIImageBlurShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageBlurShaderv0_0_5_d373ff2f_fef0_42f7_9346_18f4a6b60d2a.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageBlurShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "ed628ce1-ad0a-4219-8498-8a2043eff562", "description": "", "name": "UIImageBlurFocusShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageBlurFocusShaderv0_0_5_07ff1328_710a_4d28_9caa_495831de703e.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageBlurFocusShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "40abb7fd-79e8-41fb-8d48-a7a2ffb4e5dc", "description": "", "name": "UIImageBlurAdvancedShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageBlurAdvancedShaderv0_0_5_0445866c_afe2_44e6_aefe_8186c88aaf1a.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageBlurAdvancedShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "15becdf6-4b87-4931-8b5c-98c21a6307f5", "description": "", "name": "UIImageBloomShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.8", "latestDownloadUrl": "http://file.liangxiegame.com/UIImageBloomShaderv0_0_8_8ff07282_1413_4bab_af5b_7bc11d25b3bf.unitypackage", "installPath": "Assets/QFramework/Effect/UIImageBloomShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "f2c805ff-b2f7-4a7a-9004-89f2e0d07247", "description": "", "name": "TypeEventSystem", "author": "liang<PERSON>e", "latestVersion": "v0.0.0", "latestDownloadUrl": "empty", "installPath": "empty", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "c2379f9a-05cb-44d0-8181-fb3ff522b41c", "description": "", "name": "SuperTiled2Unity", "author": "shiyuan", "latestVersion": "v0.0.1", "latestDownloadUrl": "http://file.liangxiegame.com/SuperTiled2Unityv0_0_1_930a7d9e_57b7_406d_939c_a94eba553468.unitypackage", "installPath": "Assets/SuperTiled2Unity/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "f4660399-cd72-47b8-8da1-1e70b0188d1f", "description": "", "name": "<PERSON><PERSON>", "author": "liang<PERSON>e", "latestVersion": "v0.1.1", "latestDownloadUrl": "http://file.liangxiegame.com/Singletonv0_1_1_3b6c3abf_ceeb_4cfd_b83b_d1960a47659c.unitypackage", "installPath": "Assets/QFramework/Framework/1.Core/Singleton/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "ca6af905-a088-4247-852a-aeb74bcae6d5", "description": "", "name": "SimpleEventSystemExample", "author": "liang<PERSON>e", "latestVersion": "v0.0.2", "latestDownloadUrl": "http://file.liangxiegame.com/SimpleEventSystemExamplev0_0_2_a0b19984_2a94_40d2_94b5_e55fce3fca8e.unitypackage", "installPath": "Assets/QFramework/Extensions/SimpleEventSystemExample/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "147c30e4-da22-4e11-ac1f-f32c8f56f4b6", "description": "", "name": "SimpleComponents", "author": "liang<PERSON>e", "latestVersion": "v0.1.0", "latestDownloadUrl": "https://file.liangxiegame.com/SimpleComponentsv0_1_0_51dfcbe1_349e_4422_8b05_fbc3d218e359.unitypackage", "installPath": "Assets/QFramework/SimpleComponents/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "82a8a749-3cd0-4667-bdf9-75f2d4e3dfd2", "description": "", "name": "SimpleCameraControl", "author": "Starry", "latestVersion": "v0.0.3", "latestDownloadUrl": "http://file.liangxiegame.com/SimpleCameraControlv0_0_3_716f4f2b_f78a_41d3_b689_5e803ab1a4cb.unitypackage", "installPath": "Assets/SimpleCameraControl/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "08c0e5e6-f9d5-4b1b-8819-030279683ccf", "description": "##  介绍", "name": "ShaderLib", "author": "liang<PERSON>e", "latestVersion": "v0.2.0", "latestDownloadUrl": "http://file.liangxiegame.com/ShaderLibv0_2_0_ac6a5ab5_43da_4f7b_b75e_8b4ec117d7e2.unitypackage", "installPath": "Assets/QFramework/Shaders/ShaderLib/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "988228e7-be55-430c-bbd5-4f6d64ed1ad6", "description": "", "name": "ScriptKitXLua", "author": "liang<PERSON>e", "latestVersion": "v0.1.0", "latestDownloadUrl": "http://file.liangxiegame.com/ScriptKitXLuav0_1_0_88fc2404_6203_4744_bbb9_d55f1def9d1a.unitypackage", "installPath": "Assets/ScriptKitXLua/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "353a4545-b7ed-423e-82f6-efff229b489a", "description": "", "name": "ScriptKitVisualScripting", "author": "liang<PERSON>e", "latestVersion": "v0.0.2", "latestDownloadUrl": "https://file.liangxiegame.com/ScriptKitVisualScriptingv0_0_2_5cf74a13_f03d_40db_85a0_33ae6895e068.unitypackage", "installPath": "Assets/QFramework/Scripting/ScriptKitVisualScripting/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "84b96d49-f0ec-43e2-9522-e45bf6425d6e", "description": "", "name": "ScriptKitToLua", "author": "liang<PERSON>e", "latestVersion": "v0.2.1", "latestDownloadUrl": "https://file.liangxiegame.com/ScriptKitToLuav0_2_1_8d05a1f5_fe73_4bd1_84eb_1678cca73d2d.unitypackage", "installPath": "Assets/QFramework/Scripting/ScriptKitToLua/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "105fb2fa-831b-47d3-8ae8-cb6b32604307", "description": "", "name": "ScriptKitMoonSharp", "author": "liang<PERSON>e", "latestVersion": "v0.1.0", "latestDownloadUrl": "http://file.liangxiegame.com/ScriptKitMoonSharpv0_1_0_5b948850_035c_40e8_8ffe_403edc3d92c7.unitypackage", "installPath": "Assets/ScriptKitMoonSharp/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "c3fa3099-1212-43e8-b27f-9ba0e5c29998", "description": "", "name": "ScriptKitJSBinding", "author": "liang<PERSON>e", "latestVersion": "v0.0.1", "latestDownloadUrl": "http://file.liangxiegame.com/ScriptKitJSBindingv0_0_1_fa60c93c_94af_4746_b4aa_ebba5e9a053e.unitypackage", "installPath": "Assets/ScriptKitJSBinding/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "fd073e1a-39ac-4bf2-9214-ece4eacd2e0a", "description": "", "name": "ScriptKitILRuntimeTetrisDemo", "author": "liang<PERSON>e", "latestVersion": "v0.3.7", "latestDownloadUrl": "https://file.liangxiegame.com/ScriptKitILRuntimeTetrisDemov0_3_7_bb8bdd97_1f13_4875_9573_97cf9603f350.unitypackage", "installPath": "Assets/ScriptKitILRuntimeTetrisDemo/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "48547f1e-8895-4751-a695-0fb9863e9453", "description": "", "name": "ScriptKitILRuntime", "author": "liang<PERSON>e", "latestVersion": "v0.9.17", "latestDownloadUrl": "https://file.liangxiegame.com/ScriptKitILRuntimev0_9_17_8645c62f_0df3_44db_a09d_8d3c894df645.unitypackage", "installPath": "Assets/QFramework/Scripting/ScriptKitILRuntime/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "8a804165-b7f3-4935-b9e8-0f4471c01fb7", "description": "一套脚本 API 适配所有的 脚本语言:\n\n- [ ] To<PERSON>ua（正在支持）\n- [ ] sLua\n- [ ] xLua\n- [ ] ILRuntime\n- [ ] JSBinding\n- [ ] 单纯的反射支持", "name": "ScriptKit", "author": "liang<PERSON>e", "latestVersion": "v0.3.1", "latestDownloadUrl": "https://file.liangxiegame.com/ScriptKitv0_3_1_b17ec859_f9f7_4dbc_9a16_34e0012247ad.unitypackage", "installPath": "Assets/QFramework/Scripting/ScriptKit/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "357ec511-16ec-4d17-980e-0a0afcbe891d", "description": "将日志写入文件的一个工具。（减少加班利器）\n\n## 基本使用\n将 QLog 挂在一个 不会销毁的 GameObject 上。\n\n## 日志查看\n直接找到对应平台的 persistentDataPath 目录里就能看到日志了。", "name": "QLog", "author": "liang<PERSON>e", "latestVersion": "v0.1.0", "latestDownloadUrl": "http://file.liangxiegame.com/QLogv0_1_0_3831e6c4_63e6_4f13_a750_23e766b0da61.unitypackage", "installPath": "Assets/QFramework/QLog/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "790b369b-1538-412b-b52c-156b844908f3", "description": "", "name": "QConsole", "author": "liang<PERSON>e", "latestVersion": "v0.0.7", "latestDownloadUrl": "https://file.liangxiegame.com/QConsolev0_0_7_7b05b996_326b_4b2f_967b_1e348df8b769.unitypackage", "installPath": "Assets/QFramework/Tools/QConsole/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "3df464f1-9dfe-45af-97e0-c6dde3e293d8", "description": "", "name": "ProtobufNet", "author": "liang<PERSON>e", "latestVersion": "v0.0.0", "latestDownloadUrl": "empty", "installPath": "empty", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "784ea34c-2765-4657-a5eb-5705ae7dace4", "description": "", "name": "PostFXSnowShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXSnowShaderv0_0_5_08741ccd_e100_4ee6_bf58_0e8995358af8.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXSnowShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "be127380-fbe0-44f4-9867-09c30e94a65d", "description": "", "name": "PostFXScreenRaindropShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.6", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXScreenRaindropShaderv0_0_6_f1d0fdbe_45e3_4738_b2f7_3c80d7bf69cf.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXScreenRaindropShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "0c0f5e62-9679-4cfe-96b6-9c0381f14f5d", "description": "", "name": "PostFXRain<PERSON><PERSON>er", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXRainShaderv0_0_4_62001db3_11a7_49ca_a542_d74621cf13e1.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXRainShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "06457ee0-7b77-4ada-8eaa-027ed06de14c", "description": "", "name": "PostFXLavaShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXLavaShaderv0_0_4_61abd2bb_79b3_4a64_886d_12172f4a450c.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXLavaShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "ababc669-ed7e-450a-a573-04c5e051879e", "description": "颜色设置灰色效果\n\n\n使用方法 \n\n1. 在 Camera 上挂上 PostFXBehaviour\n2. 将 PostGray Shader 挂到  PostFXBehaviour 的 Shader 属性上。\n", "name": "PostFXGrayS<PERSON>er", "author": "liang<PERSON>e", "latestVersion": "v0.0.7", "latestDownloadUrl": "https://file.liangxiegame.com/PostFXGrayShaderv0_0_7_7b58e19e_719f_4e74_aaa3_224a638fa802.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXGrayShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "4fb082ff-ea29-4182-9d63-a401465b0e5a", "description": "", "name": "PostFXFireParticleShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXFireParticleShaderv0_0_5_fce5e3ca_b985_4ada_8293_cc3b07911c88.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXFireParticleShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "9a8ca8aa-c3d4-46b5-bf85-1d51f01375f0", "description": "", "name": "PostFXBlurShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "http://file.liangxiegame.com/PostFXBlurShaderv0_0_5_53053851_c2c8_4fd9_803a_5ae725234e1f.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXBlurShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "23d4d8d2-4c2f-4bd4-8a78-1daba22b6e83", "description": "", "name": "PostFXBloomShader", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "https://file.liangxiegame.com/PostFXBloomShaderv0_0_5_adfeca5f_f3b4_4659_aaac_898b9d844220.unitypackage", "installPath": "Assets/QFramework/Effect/PostFXBloomShader/", "includeFileOrFolders": [], "accessRight": "public", "type": "Shader", "isOfficial": false}, {"id": "1ac07d60-f622-4289-a02d-479751e208b3", "description": "", "name": "PostFX", "author": "liang<PERSON>e", "latestVersion": "v0.0.1", "latestDownloadUrl": "https://file.liangxiegame.com/PostFXv0_0_1_96a70ff6_95e6_4682_b609_32be01dafcdc.unitypackage", "installPath": "Assets/QFramework/Effect/PostFX/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "c8d98aad-d198-4678-90f1-06491c5de94c", "description": "<PERSON><PERSON><PERSON>das<PERSON>", "name": "Playground", "author": "liang<PERSON>e", "latestVersion": "v0.0.1", "latestDownloadUrl": "https://file.liangxiegame.com/Playgroundv0_0_1_2e90896d_a37d_45fe_a4db_0c2a86e89fb4.unitypackage", "installPath": "Assets/Playground/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "31d29cab-db78-49e0-a5bf-7f2fd27a26aa", "description": "", "name": "PixelKit", "author": "liang<PERSON>e", "latestVersion": "v0.0.5", "latestDownloadUrl": "https://file.liangxiegame.com/PixelKitv0_0_5_c8d9367e_5fce_47fc_97a6_399606c0ac37.unitypackage", "installPath": "Assets/QFramework/PixelKit/", "includeFileOrFolders": [], "accessRight": "public", "type": "Plugin", "isOfficial": false}, {"id": "0f040ccb-9756-4c8a-b9e6-9edecfed8fb2", "description": "", "name": "PackageKit", "author": "liang<PERSON>e", "latestVersion": "v1.0.2", "latestDownloadUrl": "https://file.liangxiegame.com/PackageKitv1_0_2_44b1344c_7338_445e_9efe_cc3e8fedd261.unitypackage", "installPath": "Assets/QFramework/Toolkits/PackageKit/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "ff37f45d-6360-4c62-aaf3-4355fc391ace", "description": "提交测试\r\n", "name": "<PERSON>", "author": "Leon1990", "latestVersion": "v0.0.1", "latestDownloadUrl": "https://file.liangxiegame.com/Leon Toolsv0_0_1_4212153a_decc_4ad2_861c_2f1007b44457.unitypackage", "installPath": "Assets/<PERSON>/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "a3863f11-9338-4a99-a536-631fdfc055c9", "description": "", "name": "JsonDotnetExtensions", "author": "liang<PERSON>e", "latestVersion": "v0.0.4", "latestDownloadUrl": "http://file.liangxiegame.com/JsonDotnetExtensionsv0_0_4_87489331_f417_4153_b759_9f55bf5ec774.unitypackage", "installPath": "Assets/QFramework/Extensions/JsonDotnetExtensions/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "ec9423cb-5c26-481a-8076-1974bfb2bfd5", "description": "", "name": "IceInkOhterPlugins", "author": "MoonIceInk", "latestVersion": "v0.0.117", "latestDownloadUrl": "http://file.liangxiegame.com/IceInkOhterPluginsv0_0_117_6ce999cc_4e09_45d2_aacd_948293433785.unitypackage", "installPath": "Assets/IceInkOhterPlugins/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "c19d33b5-7df4-44b5-9df3-f4c70a51c2b4", "description": "# 兼容QF的AsssetBundle的热更资源下载器\n\n## 使用前的准备:请保证工程中存在Json.net以及Unirx插件(Unirx仅供运行示例，如不需要可连同示例删除)\n***\n### 使用前的配置:\n####  1.修改HotPatchManager中的m_DownLoadPath目录，这里请与reskit的沙盒加载路径保持一致\n#### 2.修改HotPatchManager中的ReadXml方法中的 xmlUrl路径，这里请与文件服务器上的xml配置文件保持一致\n#### 3.修改BundleHotFix中的CopyAbAndGeneratJson方法中patch.Url路径，这里是服务器资源路径，\n#### (推荐使用服务器根目录+AssetBundle文件夹+版本号+版本热更次数+文件名的组合方式)\n#### 4.BundleHotFix中的m_HotPath为热更资源生成路径(有需要可自行修改)\n#### 5.SaveVersion中的m_VersionMd5Path为热更资源版本配置信息生成路径(有需要可自行修改)  \n  ***\n### 如何使用:  \n#### 1.先生成一次AB包\n#### 2.生成完成之后点击热更按钮，选择热更配置，依次点击记录版本号，生成热更资源列表。\n#### 之后可在Resources目录中找到ABMD5以及Verison文件(如未找到请刷新编辑器)\n#### 3.生成之后即可热更资源，点击打包热更包，选择版ABMD5文件，该路径请选择SaveVersion中的m_VersionMd5Path\n#### 热更补丁版本为当前热更次数，可多次累计(合理控制次数可实现版本回退)生成的AB包资源可在BundleHotFix中的m_HotPath路径下找到\n####  4.服务器配置文件已提供，打包热更包资源会生成一个xml文件，请复制其中文件信息到服务器xml文件中完成信息更新\n#### 注：服务器xml配置文件，一个GameVersion块为一个版本，一个Pathes为一次热更次数，Des为版本公告，可用来发布更新说明\n#### 示例已提供，如遇到问题可联系作者QQ1178092718", "name": "HotFixDowload", "author": "h3166179", "latestVersion": "v0.0.2", "latestDownloadUrl": "https://file.liangxiegame.com/Editorv0_0_2_5431981a_2bfd_46b9_a9c2_ef31f26ecd09.unitypackage", "installPath": "Assets/HotFixDowload/Editor/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": false}, {"id": "17e600f8-c6fa-4fdf-a77e-843e28627e2f", "description": "# QFramework 的主体包\r\n\r\n包含:\r\n\r\n* Framework\r\n* CoreKit\r\n* ResKit\r\n* UIKit\r\n* AudioKit\r\n", "name": "Framework", "author": "liang<PERSON>e", "latestVersion": "v1.0.189", "latestDownloadUrl": "https://file.liangxiegame.com/Frameworkv1_0_189_85e5290f_c4c0_4f07_9986_581df58d3bd2.unitypackage", "installPath": "Assets/QFramework/Framework/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": true}, {"id": "b6163729-1e12-47d6-87a0-c1ea5f5d2fe1", "description": "# QFramework 的扩展包(停止维护)\n\n1. 包含 DoTween、UniRx、JsonDotNet 的封装\n2. JsonSerializeHelper\n", "name": "Extensions", "author": "liang<PERSON>e", "latestVersion": "v0.0.9", "latestDownloadUrl": "https://file.liangxiegame.com/Extensionsv0_0_9_b4bbd4a0_bf52_415a_8560_4cd048b7005f.unitypackage", "installPath": "Assets/QFramework/Extensions/", "includeFileOrFolders": [], "accessRight": "public", "type": "Framework", "isOfficial": true}]}