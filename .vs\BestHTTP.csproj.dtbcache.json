{"RootPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo", "ProjectFileName": "BestHTTP.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsCredentialedDecryptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ScaleYPointMap.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\ProtectedPkiMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsCredentialedSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsAeadCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\ElGamalKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\NaccacheSternKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERTaggedObjectParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECDomainParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\BaseDigestCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Authentication\\IAuthenticationProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\RsaUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\Link.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X962Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\drbg\\HMacSP800Drbg.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECPointMap.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\ZSignedDigitR2LMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECFieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\WebGLConnection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ICipherBuilderWithKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathChecker.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\SCVPReqRes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\ISet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163R2Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\VMPCRandomGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\LimitedInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\GeneralNames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\NaccacheSternKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcX25519.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RsaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ParametersWithID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Cookies\\Cookie.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\BasicGcmExponentiator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\icao\\CscaMasterList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PKCSObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDerivationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\Pfx.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509StoreFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509CrlParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9ObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsClientContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\BufferedEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509Attributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\GZipStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Implementations\\WebGLBrowser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsHandshakeHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\TimeStampTokenEvidence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ntt\\NTTObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\UrlAndHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\SignerInformationStore.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\IAsn1Convertible.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\DHValidationParms.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\CtsBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\SHA3Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\DHBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPResp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\LazyDERSequence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Person.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\BcpgObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ISignatureFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\LazyDERSet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ChannelBinding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECCurve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsSuiteHmac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Ed448Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\AgreementUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerOctetString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\oiw\\OIWObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\GenTimeAccuracy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\MqvPublicParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Pkcs12ParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Longs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsKeyExchangeFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509CollectionStore.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Encoders\\MessagePackCSharpProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Object.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1SetParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\attr\\ImageAttrib.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AttCertIssuer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\PkcsIOException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\KeyFlags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1OctetString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\FramesAsStreamView.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\NonMemoableDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\StaticTree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\DesEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\KeyPurposeId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RFC3394WrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\PublicKeyAlgorithmTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerNumericString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDigestFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP128R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\SignedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\AttributeTypeAndValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Interfaces.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\ChaCha7539Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\NameType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\GcmUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPskKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\fpe\\FpeFf3_1Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\GUIHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsNonceGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\InfoTypeAndValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\SignaturePolicyId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\GOST3410ParamSetParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ECDHPublicBCPGKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerGeneralString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\JsonEncoders\\DefaultJSonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\RevReqContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OriginatorInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\WebSocket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastTlsBlockCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\DistributionPointName.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Ed25519phSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\icao\\DataGroupHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HeartbeatMode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\ProtectedPart.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\KCtrBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsECDHKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\DistributionPoint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\Inflate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\ResumableStreamingSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IBufferedCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DigitallySigned.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\PasswordRecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\EncryptedKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\RSASSAPSSparams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\RsaSecretBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Messages\\ServerMessages.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\OCSPRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\CmsKeyTransRecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\RevocationDetails.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIHeaderBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertRepMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\SignerInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\IX509AttributeCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\GMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsClientProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\UrlBase64Encoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPsk.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\Restriction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\MultipartFormDataStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\TypeOfBiometricData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\TextListItem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Collections\\Specialized\\NotifyCollectionChangedEventArgs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaPublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\TwofishEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECLookupTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\JSON.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\IssuerAndSerialNumber.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OriginatorIdentifierOrKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\DesEdeEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SignatureScheme.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\CipherStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Authentication\\DefaultAccessTokenAuthenticator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\IRandomGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\JsonEncoders\\IJSonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\HelperClasses.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServerContextImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\RevRepContentBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\FileSystem\\IIOService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\PrivateKeyFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\PacketTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\AbstractTls13Client.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SymmetricEncDataPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\djb\\Curve25519Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CombinedHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\TrustAnchor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Transports\\ITransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\anssi\\ANSSIObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\DesEdeWrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1StreamParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixNameConstraintValidatorException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\SignerSink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\IesEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\MacStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Transports\\LongPollingTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAttributeTableGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\SubjectDirectoryAttributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509CrlEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\NameConstraints.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TrustedAuthority.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc7748\\X25519Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsEd25519Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\SocketOptions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PKMacValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DigestInputBuffer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\RevRepContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SEEDEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC4Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsDHUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\gm\\SM2P256V1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ElGamalKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\WhirlpoolDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\Hex.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\RevocationKeyTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\PKCS5Scheme2PBEKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Integers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\CompressedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\CertStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\AsymmetricCipherKeyPair.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECPublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\StreamBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERSetParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509CertificatePair.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP128R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CertificateParsingException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\DSTU7624Mac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\ZTree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat160.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DsaKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ParametersWithSBox.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPRespStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\Asn1DigestFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SymmetricEncIntegrityPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\InfBlocks.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Memory\\BufferPool.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSessionImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Socket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDssSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastTlsBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\PkcsException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerUTCTime.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Blake2sDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\mozilla\\PublicKeyAndChallenge.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\OtherCertID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Forms\\Implementations\\HTTPMultiPartForm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathValidatorException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2PluginSettings.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\IDsaKCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherHashAlgAndValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\SelectorUI\\ExampleInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\CrlID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HeartbeatMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERBitString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\OCSPResponseStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\srp\\SRP6Utilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\PolicyInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha384Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\RecordFormat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\AbstractTlsCrypto.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\Zlib.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\WNafUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\HexEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\util\\Asn1Dump.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\RecipientId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\NullOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\KeyShareEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsDHDomain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\BCrypt.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\icao\\LDSSecurityObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\VMPCKSA3Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Exception.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ParametersWithRandom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AlgorithmIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\PrincipalUtil.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSCompressedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509CertificateStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertReqMessages.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\CRC32.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\BasicOCSPRespGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\ProtectedPkiMessageBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsECDomain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Forms\\HTTPFormBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\AuthEnvelopedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha224Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\InvalidParameterException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ModDetectionCodePacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSCompressedDataStreamGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SkipjackEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsECDHanonKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HandshakeType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\HubWithPreAuthorizationSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\DsaSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP384R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\ParameterUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SupplementalDataEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Messages\\ClientMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerApplicationSpecific.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthenticatedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableListProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\ChaChaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\bc\\LinkedCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\Revocable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\GF2Polynomial.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Enums.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9ECParametersHolder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\DSAParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerSequence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\util\\Pack.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\CSHAKEDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerStringBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPUpdateDelegator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192K1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AttributeCertificateInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\DefaultTls13Client.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R2Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\DHAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedAeadBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\NoSuchAlgorithmException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsSrp6Server.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KeyTransRecipientInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsEd25519Verifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixPolicyNode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\RevocationReasonTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\X923Padding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X448PrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Messages\\Message.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410PrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSProcessableInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\NetscapeRevocationURL.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\OfferedPsks.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\GOST3411Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RsaPrivateCrtKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\Future.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat256.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\BigIntegers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertTemplate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\PskTlsServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\EncryptedValueBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\sigi\\SigIObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\kdf\\DHKekGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ChangeCipherSpec.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\RecordStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\NaccacheSternEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsEpoch.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\PublicSubkeyPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\AriaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\WTauNafMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\MD2Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERSetParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\ErrorMsgContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\IPolynomial.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha512tDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\AsymmetricKeyParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Transports\\PollingTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\KDFDoublePipelineIterationBytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcSsl3Hmac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\sec\\SECNamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SrtpProtectionProfile.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\DHParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SocketIO\\SocketIOChatSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\BasicConstraints.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\KeyValuePairList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\IL2CPP\\PreserveAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\LinkedDictionary.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\PEMParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\IFiniteField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServerCertificateImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\RecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\BasicOCSPResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\DefaultLogger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CertificateNotYetValidException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsStreamVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Extensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Transports\\WebSocketTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\AuthEnvelopedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\MiscObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\PublicKeyEncSessionPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224K1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\srp\\SRP6Client.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\SkeinParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Transports\\WebsocketTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\PEMWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\NullDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\RespID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\HostDefinition.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixAttrCertPathValidator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\CryptoSignatureAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthEnvelopedGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\microsoft\\MicrosoftObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\FpeParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\DSTU7564Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\RsaPublicBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\IJsonWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\CryptoHashAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\ZeroBytePadding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampToken.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsRsaVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509V1CertificateGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECPoint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Hubs\\Hub.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\CMSAttributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\SCrypt.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IVerifierFactoryProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\Ssl3Utilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\NewSessionTicket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\Primes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\DefaultSignatureCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Threading\\LockHelpers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9ECParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\TypeExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSProcessableFile.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Iso9796d2PssSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\WebSocketResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMECapability.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SymmetricKeyAlgorithmTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\bc\\BCObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\GcmSivBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\StreamList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IRawAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\PluginEvents.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc7748\\X448Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\EnvelopedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\ocsp\\CertHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\RsaDigestSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\HeaderValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc7748\\X448.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsStreamVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\UploadHubSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsEncryptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163R2Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Strings.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509AttrCertStoreSelector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\ParallelHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2Frames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CrlValidatedID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\GeneralDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509AttrCertParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X25519PublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsReliableHandshake.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\Target.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\RipeMD320Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\RespData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\MacData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\Srp6Group.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\AesEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDsaVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\KeyExpirationTime.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\SicBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IStreamCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERSequenceParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\TeeInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\UserMappingType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsAuthentication.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\SecureRandom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\HubWithAuthorizationSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP384R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\ValidityPreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpIdentity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\UseSrtpData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSUtils.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AuthorityInformationAccess.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ElGamalPublicBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\NoekeonEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcChaCha20Poly1305.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\QCStatement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC2WrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RsaKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\Time.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Encoders\\JsonDotNetEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\ResponseData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableDictionaryProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\ProfessionInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Packet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakePrimeOrderGroups.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\WriteOnlyBufferedStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertOrEncCert.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\MemoableResetException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsFatalAlertReceived.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Ed448phSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\S2k.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\iana\\IANAObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\KeyDerivationFunc.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerIA5String.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\FixedPointUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\EncryptionScheme.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastChaChaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSecret.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\ESSCertIDv2.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\JsonEncoders\\DefaultJsonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\LiteralDataPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcX448.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathValidator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\gm\\SM2P256V1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\KeyUsage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHPrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\KDFCounterBytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\SigningCertificateV2.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP521R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\ESSCertID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\X448KeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\GeneralSubtree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\GlvTypeBEndomorphism.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\SubsequentMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ProtocolVersion.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\PkiArchiveControl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\CertStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\MetaData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCryptoParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\DefaultSignedAttributeTableGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPath.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\MemoryOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Events\\TypedEventTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DefiniteLengthInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CertificateValues.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ClientAuthenticationType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\SM2KeyExchangePrivateParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\ETSIQCObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixAttrCertPathBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RC5Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsDHGroupVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedCipherBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CRLNumber.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\OtherName.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\Timeout.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\PolicyQualifierId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERSetGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509CollectionStoreParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT239K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ICipherBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\OnePassSignaturePacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPReq.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\PreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\SM2KeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\RecipientInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\NaccacheSternKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\ISO7816d4Padding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPRange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9IntegerConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\IssuingDistributionPoint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\CertificateConfirmationContentBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ParametersWithSalt.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakeRound2Payload.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\NaccacheSternPrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1OutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509NameEntryConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\SignerInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\SignaturePolicyIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DeferredHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\VMPCEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PollReqContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Socket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc7748\\X25519.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BEROctetStringGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsECDheKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\Attribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\FiniteFields.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\IX509Selector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\ReversedWindowGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\Tables8kGcmMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\kdf\\DHKdfParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Transports\\ServerSentEventsTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PopoDecKeyChallContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertReqMsg.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDsaSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CompleteRevocationRefs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ICipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\FixedPointPreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\CompressionAlgorithmTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\Holder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\PskKeyExchangeMode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DesKeyGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\IL2CPP\\Il2CppSetOptionAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\CryptoException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcX25519Domain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\drbg\\ISP80090Drbg.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509StoreException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\OpenBsdBCrypt.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherCertID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509CrlStoreSelector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Implementations\\OverHTTP1.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\ECGOST3410Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Forms\\Implementations\\HTTPUrlEncodedForm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RFC3211WrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\kdf\\ECDHKekGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\KDFFeedbackParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\RFC3739QCObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Transports\\WebSocketTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsServerProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\IEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IStreamCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\MacAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Transports\\PostSendTransportBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerUTF8String.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSrp6Client.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UserAttributeSubpacketTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\ISISMTTObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\GOST28147Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\GOST3410KeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\DigestSink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\util\\BasicAlphabetMapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509V2CRLGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZInflaterInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\TcpClient\\WinRT\\DataReaderWriterStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IMacDerivationFunction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc8032\\Ed448.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\Features.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\util\\CipherFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\OtherInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\OpenSSLPBEParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\extension\\X509ExtensionUtil.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\GenericPolynomialExtensionField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMECapabilitiesAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\BcpgInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\OtherSigningCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixAttrCertChecker.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\HashSet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DesEdeKeyGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC532Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\Certificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\ECEndomorphism.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\ProofOfPossession.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastTlsAeadCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\ElGamalParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat320.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathValidatorResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\Base64Encoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\AbstractECLookupTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\CertificateRequestMessageBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Proxies\\SOCKSProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\OriginatorInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\RegTokenControl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\MPInteger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\IHTTPRequestHandler.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDsa13Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\KeyRecRepContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\MarkerPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509CertPairParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs12Utilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Caching\\HTTPCacheFileLock.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDSA.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMECapabilityVector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509ExtensionBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\Req.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\ecc\\MQVuserKeyingMaterial.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\CAST5CBCParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\NamingAuthority.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\tsp\\TimeStampResp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\Targets.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CRLReason.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DatagramSender.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerBoolean.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\ServiceLocator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SerpentEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\CertificateStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193R2Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\MonetaryLimit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsNullNullCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\WNafL2RMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMEEncryptionKeyPreferenceAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\RecipientEncryptedKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\AuthenticatedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\GOST28147Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\RipeMD256Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\Attribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERSequenceGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\ServerSentEvents\\Message.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OcspResponsesID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\DsaSecretBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\abc\\ZTauElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\FileConnection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateUrl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CachedInformationType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableSetProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsClientProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R2FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerVideotexString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\edec\\EdECObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsDHConfig.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs10CertificationRequestDelaySigned.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalR\\Json Encoders\\JSonDotnetEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\IAeadBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed448PrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\MacSink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Iso9796d2Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\PEMUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\IPreCompCallback.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\HexTranslator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DHBasicKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Pkcs5S2ParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IBlockResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\InfTree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ServerName.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CmpCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed448KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\BaseOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIMessages.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2FrameHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDH.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT239Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\RevAnnContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\UploadStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsNonceGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TSPUtil.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\DHStandardGroups.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Plugin\\AsyncExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\AdditionalInformationSyntax.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DHKeyGeneratorHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\WTauNafPreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Blake2bDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\HeaderParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\HostManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertConfirmContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\ZlibBaseStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X448PublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathBuilderException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\MgfParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\UrlBase64.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1TaggedObjectParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RsaKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSProcessableByteArray.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SrpTlsServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CrlAnnContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\CircularBuffer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\anssi\\ANSSINamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsEncodeResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\ReadOnlyBufferedStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixNameConstraintValidator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsExtensionsUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemObjectGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\TcpClient\\WinRT\\TcpClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\KeyTransRecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\Attribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\HTTPRequestAsyncExtensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\TimeStampedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\Streams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x500\\DirectoryString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Pkcs5S1ParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\IL2CPP\\Il2CppEagerStaticClassConstructionAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\Tables64kGcmMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\PeekableIncomingSegmentStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\InputStreamPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\Pkcs8Generator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\DefaultVerifierResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECPrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\SipHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthEnvelopedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SupplementalDataType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Connection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TCPConnector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerTaggedObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Frames\\WebSocketFrame.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedAsymmetricBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCrypto.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9ECPoint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PKIPublicationInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ServerOnlyTlsAuthentication.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\ECGOST3410NamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\TBSCertificateStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DatagramTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\KeyExchangeAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\HKDFBytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\abc\\SimpleBigDecimal.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SocketIO\\SocketIO Json Encoders\\JsonDotNetEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AttributeCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\RecipientKeyIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\DeflateStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\CertificateID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSrp6VerifierGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CounterSignatureDigestCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ECSecretBCPGKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ECCurveType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcDefaultTlsCredentialedAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\ContentInfoParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC2Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\GCMBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\Cast6Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsRsaSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\DefaultVerifierCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTPProtocolFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x500\\style\\IetfUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSession.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerNull.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\GlvTypeAParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\ThreefishEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\PbeUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\Components\\Cache.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ec\\CustomNamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113R2Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Messages\\Invocation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\KeyUpdateRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\nist\\NISTNamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\OCBBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\SampleBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CertificateList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\Extensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\BasicTlsPskIdentity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\RevocationKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsAeadCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\TbcPadding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ParametersWithIV.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\CertificateRequestMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Authentication Providers\\HeaderAuthenticator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\SelectorUI\\Category.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\AbstractTlsSecret.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ElGamalPublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\SignerUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\MD5Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\Lexer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SessionParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\encodings\\ISO9796d1Encoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\Base64.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CertificateEncodingException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\djb\\Curve25519Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Extensions\\IExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\IsoTrailers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Srp6GroupParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Timings\\TimingEventNames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\kdf\\ConcatenationKdfGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZTree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemGenerationException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP384R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\UnityOutput.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\drbg\\HashSP800Drbg.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\IDrbgProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerEnumerated.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\Deflate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsHeartbeat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\SocketManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHPublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ServerSrpParams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Implementations\\Utils\\LockedBufferSegmenStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\AbstractECMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsNoCloseNotifyException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerUniversalString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerGeneralizedTime.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1SequenceParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SymmetricKeyEncSessionPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\HKDFParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\ContentIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\DsaDigestSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\BufferHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\CcmParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\GOST28147Mac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Threading\\ThreadedRunner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\ParserToken.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\PlainDsaEncoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\fpe\\SP80038G.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDHDomain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OriginatorPublicKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Encodable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\DigestStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\NafL2RMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsPeer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsImplUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\JZlib.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\ESFAttributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Transports\\PollingTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\Attributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\DSTU7564Mac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\Timer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsKeyExchangeFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\nsri\\NsriObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC6Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERSequenceParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP128R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Authentication\\Credentials.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\SemanticsInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\SigningCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT239FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\OptionalValidity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\IX509Store.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\Asn1CipherBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\Inflate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs10CertificationRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Tags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ICipherParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\IdentifierType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\KDFDoublePipelineIterationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\Translator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\BcpgOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\ResponderID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IKeyWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\Challenge.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthenticatedDataStreamGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ua\\UAObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509Utilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPskExternal.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Ed25519Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1ParsingException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\RSAESOAEPparams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcX448Domain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsDHGroupVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAttributeTableGenerationException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastCcmBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServerContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\EncryptedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\Adler32.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\NamedGroup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\EnvelopedDataHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\sec\\SECObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP521R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerBMPString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\XTEAEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP384R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\RevokedStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\BufferedReadNetworkStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsRsaPssVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\CryptoProObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R2Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs12Entry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Events\\EventDescriptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerTaggedObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\GeneralPkiMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\gm\\GMObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ElGamalPrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\JsonEncoders\\IJsonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\AsyncTestHubSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsFatalAlert.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\ECMqvBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ExperimentalPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\TcpClient\\TcpClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\Pkcs7Padding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Messages\\NegotiationResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERExternal.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\HubConnection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Arrays.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\TimeStampAndCRL.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ess\\ContentHints.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsCrypto.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CmpObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SignatureAndHashAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDerivationFunction.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat448.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\BaseKdfBytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HashAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\OriginatorId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedDataGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\CmpException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\extension\\SubjectKeyIdentifierStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\OutputLengthException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakeParticipant.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\ILogger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\AssetBundleSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256K1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\encodings\\Pkcs1Encoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\OidTokenizer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\ECDHBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\X509CertificateEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\ExtendedKeyUsage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\EndoPreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\MqvPrivateParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\ZlibCodec.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\tsp\\TimeStampReq.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedIesCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X25519KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERExternalParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertChainType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\NullEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\MonetaryValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\ECDHWithKdfBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\rfc8032\\Ed25519.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\SocketManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedAeadCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UserIdPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\Asn1KeyWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\GOST3410NamedParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\MaxBytesExceededException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPMethods.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Events\\EventNames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\MontgomeryLadderMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\drbg\\DrbgUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PKCS12PBEParams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\Request.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPeer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERSequenceGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCryptoException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha512Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\SubjectKeyIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\HandshakeData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410ValidationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\Admissions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\ReferenceMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\RecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x500\\AttributeTypeAndValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BERGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsRecordLayer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\Packet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\rosstandart\\RosstandartObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SampleRoot.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\gnu\\GNUObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\CompressedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\ProofOfPossessionSigningKeyBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HuffmanEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs8EncryptedPrivateKeyInfoBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2Handler.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Encoders\\MessagePackProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\SkeinEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs8EncryptedPrivateKeyInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\PssSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CrlIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CipherType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Ed25519KeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateStatusRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\GenMsgContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\PaddedBufferedBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Transports\\TransportBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\CryptoApiEntropySourceProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UserAttributePacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsHmac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SignatureSubpacketsReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampTokenGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SocketIO\\SocketIO Json Encoders\\LitJsonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\Transports\\TransportBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\Rfc3281CertPathUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509NameTokenizer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\HostConnection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410KeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CrmfObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HPACKEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\V2TBSCertListGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509CertPairStoreSelector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastGcmBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\IBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\DsaPublicBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\PskTlsClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\SinglePubInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SignatureSubpacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IKeyUnwrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KeyAgreeRecipientInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\Check.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CrlOcspRef.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\MD4Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\WNafPreCompInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\X931Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AlertLevel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\HandshakeData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\IGcmExponentiator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\KDFCounterParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\LazyASN1InputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDsaExt.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DEROctetStringParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsRsaEncryptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\EncryptedPrivateKeyInfoFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\tsp\\MessageImprint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SM4Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\PKCS12StoreBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\CMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KEKRecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\NafR2LMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIFailureInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\VMPCMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalR\\Authentication Providers\\SampleHeaderAuthentication.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\CmsContentEncryptorBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TSPValidationException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\AuthenticatedSafe.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\EncryptionException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\SignerInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\XofUtils.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\IProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Timings\\TimingCollector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakeRound3Payload.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CrlListID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ElGamalSecretBcpgKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCertificateRole.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSrpConfig.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509CertificateParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\RipeMD160Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTPConnection.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\ChaCha20Poly1305.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\CollectionUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\BasicTlsSrpIdentity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DERSetGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedDataStreamGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\EncryptionAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableSet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\SafeBag.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixBuilderParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIBody.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Ed448KeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\RoleSyntax.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OtherKeyAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsNullCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\Deflate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\X931SecureRandomBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\SP800SecureRandomBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\GeneratorUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\SignerAttribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\Signature.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2SettingsRegistry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\MaxFragmentLength.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsDHanonKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\CbcBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\V2Form.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\RevocationReason.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\CertificateConfirmationContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\sec\\ECPrivateKeyStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\IControl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\AuthenticatorControl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\extension\\AuthorityKeyIdentifierStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\IAuthenticationProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Enums.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\DisplayText.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Set.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECNamedDomainParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\SignatureCreationTime.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\SignerIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsStreamSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\ObjectDigestInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2Stream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\GlvEndomorphism.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\Tables1kGcmExponentiator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\KEKIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509V3CertificateGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\WWWAuthenticateHeaderParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\HC256Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x500\\Rdn.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\HashAlgorithmTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\encoders\\BufferedDecoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonMockWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SignatureSubpacketTags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509V2AttributeCertificateGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPskIdentity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PBEParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X962NamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\KeySpecificInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\ISO10126d2Padding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\RecipientInformationStore.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\GeneralSecurityException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\KdfParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerObjectIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Websocket\\WebSocketSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\HC128Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SocketIO3\\Parsers\\MsgPackParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZDeflaterOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\ECNRSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\RSAPublicKeyStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\DHGroup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PBKDF2Params.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IWrapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\TeeOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\CertBag.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\teletrust\\TeleTrusTNamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\gm\\SM2P256V1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsStreamSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMEAttributes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\DotNetUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\AdmissionSyntax.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\ECNamedCurveTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\CamelliaWrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\Pkcs12Store.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\CMSObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\IPolynomialExtensionField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\EnvelopedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UserAttributeSubpacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\CompressedDataPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PrivateKeyInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\StreamingSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\RevokedInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CertificatePair.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\PolicyMappings.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\CertificateStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256K1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PopoPrivKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\InfTree.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherSigningCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\KMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\smime\\SMIMECapabilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\KeyLogFileWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CertificatePolicies.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\X25519KeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\PushbackStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSStreamException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastChaCha7539Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SecretKeyPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsEd448Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\eac\\EACObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\SignerUserId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat128.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\StreamOverflowException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedDataStreamGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\BiometricData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\ServerSentEvents\\EventSource.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPUtil.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\EncryptedPrivateKeyInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\RSABlindingFactorGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\HMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Poly1305KeyGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\TargetInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SerpentEngineBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\ECMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpLoginParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KEKRecipientInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ServerHello.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\AttributeTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\OpenPgpCfbBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\djb\\Curve25519FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\FastTlsCrypto.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SM2Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\AEADParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\GOST3410DigestSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\CertificationRequestInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSrp6Server.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\Rfc3280CertPathUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\nist\\KMACwithSHAKE256_params.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\KEKRecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\SubjectPublicKeyInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\Ed25519ctxSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DSAParameterGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDomain.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\TBSRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\Security.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\CamelliaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\CcmBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\KeyAgreeRecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\Salsa20Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat576.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\ISAACEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Error.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\EncryptedContentInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\SigPolicyQualifierInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PbmParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsRsaPssSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\X448Agreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedStreamCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BEROctetStringParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\EncKeyWithID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDecryptorBuilderProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\net\\IPAddress.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1TaggedObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP521R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\KDFFeedbackBytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerSet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\InfCodes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\BufferSegmentStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampTokenInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\NotationData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\V3TBSCertificateGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\LongDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\StandardDsaEncoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\CipherUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\AesLightEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\IesWithCipherParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\IPKMacPrimitivesProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AlertDescription.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\SecurityUtilityException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpIdentityManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DHKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\GlvTypeBParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\Poly1305.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\icao\\LDSVersionInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\HTTP\\TextureDownloadSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\SignerInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Cookies\\CookieJar.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\CryptoApiRandomGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSCompressedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\OCSPObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\TweakableBlockCipherParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsCryptoUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\date\\DateTimeUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RC2Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\GlvTypeAEndomorphism.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP128R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RSABlindingEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\PrivateKeyInfoFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\GOST3411_2012_512Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerPrintableString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\RequestEvents.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KeyTransRecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ConstructedOctetStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServerProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SocketIO3\\ChatSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IVerifierFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\OcspStatusRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\IAeadCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\GenRepContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\OutputStreamPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\EnumerableProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X25519PrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\BasicEntropySourceProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIFreeText.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Transports\\WebSocketTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\SignatureExpirationTime.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\ScalarSplitParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\CamelliaLightEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\srp\\SRP6Server.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDsaVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSecureReadable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat384.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Authentication\\DigestStore.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\SM2KeyExchangePublicParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalR\\Json Encoders\\LitJsonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\DigestInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\IdeaEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\TigerDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonWriter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha1Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113R2Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerInteger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Events\\EventNames.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsSecret.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Events\\EventTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IAsymmetricBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\SkeinDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Hubs\\IHub.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerSequence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\IndefiniteLengthInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\AesWrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OtherRevocationInfoFormat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed25519KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\gm\\GMNamedCurves.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\PasswordRecipientInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Messages\\IServerMessage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\WebSocketStatusCodes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131R2Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\RedirectSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsCredentials.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ArmoredOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CertificateException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\SignatureException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IEntropySource.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcDefaultTlsCredentialedDecryptor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\TEAEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\ConnectionHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSProcessable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UnsupportedPacketVersionException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CommitmentTypeQualifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\ISO9797Alg3Mac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\qualified\\Iso4217CurrencyCode.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\FileOutput.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Implementations\\OverHTTP2.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsHmac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP1Handler.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\BasicOCSPResp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\OobCertHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\FixedPointCombMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DatagramReceiver.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193R2Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\PEMReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\zlib\\ZStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\FileSystem\\DefaultIOService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\teletrust\\TeleTrusTObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\NetscapeCertType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\nist\\NISTObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\PrivateKeyUsagePeriod.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\GOST3410PublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ByteQueueOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Interleave.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\IAsn1String.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\RC2CBCParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthenticatedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\Dstu7624Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\PskIdentity.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDH.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\GOST3411_2012_256Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\DoubleAddMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\GOST3410ParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\SelectorUI\\SampleSelectorUI.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\SubjectPublicKeyInfoFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\drbg\\CtrSP800Drbg.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIConfirmContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509KeyUsage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\Evidence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ElGamalParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cmp\\RevocationDetailsBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathBuilderResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthenticatedDataGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\sigi\\NameOrPseudonym.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\IExtensionField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\PKMacBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\IAsn1Choice.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ClientHello.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastTlsAeadCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\HeartbeatManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\ProcurationSyntax.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\Components\\Cookies.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\GeneralName.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\EmptyEnumerable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsSrpConfigVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPReqGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509Certificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonMapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\PublicKeyFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\JSON\\LitJson\\JsonReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsEd448Verifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\X9FieldID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\util\\CipherKeyGeneratorFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Parsers\\IParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT193R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\NegotiationData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsDheKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsCloseable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\RevDetails.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CAKeyUpdAnnContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\icao\\ICAOObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakeRound1Payload.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCrlUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\IEncryptedValuePadder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\HTTPRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastBcChaCha20Poly1305.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\GenericSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ServerNameList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\CertTemplateBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ExtensionType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Mod.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ECPointFormat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\EncryptedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateStatusType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSAuthenticatedGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\SignedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPRespGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\JsonProtocol.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsClientContextImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CompressionMethod.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\SP800SecureRandom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Timings\\TimingEvent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\OriginatorInformation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsSrp6VerifierGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AuthorityKeyIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\TrustPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DHValidationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\util\\FilterStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\PkiArchiveControlBuilder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ProtocolName.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\V2AttributeCertificateInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\srp\\SRP6StandardGroups.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\SEEDWrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSSignedGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DesEdeParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Frames\\WebSocketFrameTypes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\DHPublicKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ECPublicBCPGKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Kdf2BytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\PreferredAlgorithms.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Proxies\\HTTPProxy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Bits.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\MiscPemGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CipherSuite.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509Crl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IMacFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\BaseInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\ResponseBytes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IAlphabetMapper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherRevRefs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\CipherKeyGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PopoSigningKeyInput.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\PasswordException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\DigestUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\UserAttributeSubpacketsReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\SignedData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OcspIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509Name.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsDHKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\ThreadedSeedGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IXof.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerNull.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\WrapperUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1EncodableVector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT113R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509Extensions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\PKCS5Scheme2UTF8PBEKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\GOFBBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\Dstu7624WrapEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\TnepresEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\IDsaEncoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\abc\\Tnaf.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\paddings\\BlockCipherPadding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsHeartbeat.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampRequestGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TSPAlgorithms.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\BigInteger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsHashSink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\NamedGroupRole.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\RecipientIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\NoSuchStoreException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\RSAPrivateKeyStructure.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastCbcBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\GOST3411_2012Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IRsa.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcVerifyingStreamSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkcs\\AsymmetricKeyEntry.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AttCertValidityPeriod.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\EntropyUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\PrimaryUserId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\SimpleBlockResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\Encoders\\LitJsonEncoder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ECDsaPublicBCPGKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\BasicTlsPskExternal.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalRCore\\UploadItemController.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\sigi\\PersonalData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\ShakeDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HeaderTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\CertificateStatusRequestItemV2.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsDecodeResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\util\\AlgorithmIdentifierFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalRCore\\TestHubSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\RevocationValues.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Extensions\\PerMessageCompression.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIStatusInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP521R1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampResponseGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\collections\\UnmodifiableDictionary.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RijndaelEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemHeader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Kdf1BytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\djb\\Curve25519.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIHeader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\Cast5Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\GenericKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\pem\\PemObjectParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\endo\\EndoUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509DefaultEntryConverter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\ThreadedLogger.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT131R2Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ExporterLabel.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\ECKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ISignerWithRecovery.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSPBEKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\OtherRecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\SkeinMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509ExtensionsGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\PEMException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsServer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsCredentialedAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ECAlgorithms.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\UserNotice.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSTypedStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\bsi\\BsiObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PKIArchiveOptions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1InputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Enums.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RSABlindedEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509V2AttributeCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\RsaKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\ISigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsPskIdentityManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\OfbBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrtpUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\InvalidCipherTextException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\PkixCertPathValidatorUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Parsers\\DefaultJsonParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\DefaultPKMacPrimitivesProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\tsp\\Accuracy.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\AttributeCertificateIssuer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Interfaces.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ContainedPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DHParametersHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\ZlibConstants.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SrpTlsClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ElGamalKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509ObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SimulatedTlsSrpIdentityManager.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\GOST3410PublicKeyAlgParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedHelper.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TSPException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\GlvMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CertificateExpiredException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsServerCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\Sha256Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Times.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastSalsa20Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\SignerId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsReassembler.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\Time.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\FileSystem\\NETFXCOREIOService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerApplicationSpecificParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerGraphicString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\ConnectionBase.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\IMemoable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Authentication\\Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\BlowfishEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\MacUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\BufferedBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\ContentInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DsaParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\PbeParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\SM2Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Enums.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakePrimeOrderGroup.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\PasswordRecipientInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tsp\\TimeStampRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\ContentInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\ArmoredInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ReflectionHelpers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\IAsn1ApplicationSpecificParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\srp\\SRP6VerifierGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\DHParametersGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Frames\\WebSocketFrameReader.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509SignatureUtil.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\IDEACBCPar.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Platform.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDsaSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IAsymmetricCipherKeyPairGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HeartbeatExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\TimeStampedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\CbcBlockCipherMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SecurityParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\kisa\\KISAObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\IX509Extension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Extensions\\BufferPoolMemoryStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsBlockCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsDssVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\PrfAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerOctetString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AccessDescription.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\pkix\\ReasonsMask.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\IncomingPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HeartbeatMessageType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsCredentialedSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\KeyParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\IDigestCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\Bytes.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\SignerLocation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\fpe\\FpeEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\X448KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ConnectionEnd.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\ECDHCBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\AesFastEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ByteQueue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\PublicKeyPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HandshakeMessageInput.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DefaultTlsKeyExchangeFactory.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\PBES2Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Collections\\Concurrent\\ConcurrentQueue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SecretSubkeyPacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PKIStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\generators\\Mgf1BytesGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\Exportable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\CertID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerOutputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Decompression\\GZipDecompressor.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\field\\PrimeField.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed25519PublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaValidationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\GOST3410Signer.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\X25519Agreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\SignalR\\Authentication Providers\\SampleCookieAuthentication.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSContentInfoParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\CertifiedKeyPair.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\LongArray.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\IssuerSerial.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastAesEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\HMacDsaKCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsAeadCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PollRepContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\KeyAgreeRecipientInfoGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\DataLengthException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\TupleHash.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat512.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ScaleYNegateXPointMap.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R2Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\RandomDsaKCalculator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\DefaultAuthenticatedAttributeTableGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\fpe\\FpeFf1Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerVisibleString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\WebSocket\\Implementations\\WebSocketBaseImplementation.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1Generator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\encodings\\OaepEncoding.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\gm\\SM2P256V1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed25519PrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsHandshakeRetransmit.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\SocketOptions.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Caching\\HTTPCacheService.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSCompressedDataGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\SingleResp.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crmf\\CrmfException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerSet.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\signers\\ECDsaSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ScaleXPointMap.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\openssl\\IPasswordFinder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat224.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\DigestRandomGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\KeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsBlockCipherImpl.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\NoticeReference.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\ShortenedDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CRLDistPoint.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CommitmentTypeIndication.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\InvalidKeyException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Caching\\HTTPCacheMaintananceParams.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\EncryptedValue.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsTimeoutException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192K1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OtherRevVals.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\Controls.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\HandshakeMessageOutput.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTPConnectionStates.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsRsaKeyExchange.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\IetfAttrSyntax.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\EncryptedContentInfoParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\io\\SignerStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\misc\\VerisignCzagExtension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\SignaturePacket.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\ElGamalEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcDefaultTlsCredentialedSigner.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerT61String.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CommitmentTypeIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ASN1OctetStringParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RSACoreEngine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\Asn1Signature.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ByteQueueInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\BerApplicationSpecific.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\EmbeddedSignature.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\oiw\\ElGamalParameter.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cmp\\PopoDecKeyRespContent.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\OCSPResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\AttributeTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\X931Rng.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224K1FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\SimpleAttributeTableGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\TrustSignature.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\CompleteCertificateRefs.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsSrpConfigVerifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\XSalsa20Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\cert\\CrlException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\X509Attribute.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\Tables4kGcmMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsObjectIdentifiers.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\macs\\CfbBlockCipherMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\engines\\RC564Engine.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\crmf\\PopoSigningKey.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\pkcs\\CertificationRequest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\PolicyQualifierInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Caching\\HTTPCacheFileInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Forms\\HTTPFormUsage.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\esf\\OcspListID.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\ConnectionEvents.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\Ed448PublicKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Server-Sent Events\\SimpleSample.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\IesParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsSrp6Client.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\X509CertStoreSelector.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\V1TBSCertificateGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\DHStandardGroups.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\Srp6StandardGroups.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO.3\\Error.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DesParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\operators\\DefaultSignatureResult.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\DerBitString.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\RecordPreview.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT571Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\DtlsReplayWindow.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\TLS\\Crypto\\Impl\\FastPoly1305.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\ReasonFlags.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\ocsp\\SingleResponse.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\store\\IX509StoreParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ECGOST3410Parameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\nist\\KMACwithSHAKE128_params.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\raw\\Nat192.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\KeccakDigest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\IGcmMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cryptopro\\ECGOST3410ParamSetParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP224R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\CfbBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Forms\\HTTPFieldData.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaKeyGenerationParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT409Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\date\\DateTimeObject.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\RSABlindingParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\ScaleXNegateYPointMap.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\RipeMD128Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Connections\\HTTP2\\HTTP2Response.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\ECMqvWithKdfBasicAgreement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP256R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\SignatureAlgorithm.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\TlsSuiteMac.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\ZSignedDigitL2RMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\AuthenticatedDataParser.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\CertPolicyId.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Sequence.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\agreement\\jpake\\JPakeUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\multiplier\\MixedNafR2LMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Examples\\Helpers\\SelectorUI\\ExampleListItem.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\X509Extension.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\IEntropySourceProvider.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\x509\\AttributeCertificateHolder.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsECConfig.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP192R1Field.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SignalR\\Transports\\PollingTransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT283R1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Core\\ProtocolEvents.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ContentType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\gcm\\BasicGcmMultiplier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\Asn1Null.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\TlsEccUtilities.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\security\\KeyException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\IssuerAndSerialNumber.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\Logger\\LoggingContext.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\ocsp\\RequestedCertificate.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\SimpleLookupTable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SocketIO\\Transports\\ITransport.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\MemoryInputStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\EAXBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecP160R1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\isismtt\\x509\\DeclarationOfMajority.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT163K1Curve.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\util\\io\\FilterStream.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\UnknownStatus.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\digests\\SM3Digest.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\DsaPrivateKeyParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT239K1Point.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x509\\TBSCertList.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\modes\\KCcmBlockCipher.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\tsp\\TSTInfo.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSEnvelopedDataGenerator.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\ClientCertificateType.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\PlatformSupport\\Collections\\ObjectModel\\ObservableDictionary.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\Crc24.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\AbstractTlsClient.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\prng\\X931SecureRandom.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\crypto\\parameters\\ISO18033KDFParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\x9\\DHDomainParameters.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\TlsMacSink.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\tls\\crypto\\impl\\bc\\BcTlsECDsa13Verifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\math\\ec\\custom\\sec\\SecT233FieldElement.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\asn1\\cms\\KeyAgreeRecipientIdentifier.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\cms\\CMSReadable.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\ocsp\\OCSPException.cs"}, {"SourceFile": "Assets\\chat-aihuman\\Add-In\\Best HTTP\\Source\\SecureProtocol\\bcpg\\sig\\IssuerKeyId.cs"}], "References": [{"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Netly\\Byter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\CSCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Demigiant\\DOTween\\DOTween.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\GifToUnity\\Editor\\GifPacked.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\Plugins\\DLL\\inmolib_common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\log4netPlastic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Runtime\\Scripts\\Common\\Framework\\QFramework\\Toolkits\\_CoreKit\\PackageKit\\Markdown\\Markdig.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.nuget.mono-cecil@1.11.4\\Mono.Cecil.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\NAudio-Unity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\Netly\\Netly.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@3.2.1\\Runtime\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collections@2.1.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\ScriptAssemblies\\UnityEditor.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\VisionOSPlayer\\UnityEditor.VisionOS.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\ScriptAssemblies\\UnityEngine.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\unityplastic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Assets\\chat-aihuman\\Add-In\\Plugins\\zxing.unity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\UnityPro\\AIHuman\\chat-aihuman_Inmo\\Temp\\bin\\Debug\\BestHTTP.dll", "OutputItemRelativePath": "BestHTTP.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}