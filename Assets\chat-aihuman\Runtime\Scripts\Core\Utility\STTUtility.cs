﻿using System;
using System.Collections;
using System.Collections.Generic;
using LitJson;
using QFramework;
using UnityEngine;
using UnityEngine.Android;
using UnityEngine.Networking;
using XM.Common;


namespace XM.Core
{
    public class STTUtility : IUtility
    {
        //protected const string m_SpeechRecognizeURL = "http://embedding.chengdu-4090-3.edge.shinemo.com:34001
        //protected const string m_SpeechRecognizeURL = "http://**************:34100";
        private void RequestMicrophonePermission()
        {
            if (!Permission.HasUserAuthorizedPermission(Permission.Microphone))
            {
                Permission.RequestUserPermission(Permission.Microphone);
            }
        }

        /// <summary>
        /// 语音识别
        /// </summary>
        /// <param name="_clip"></param>
        /// <param name="_callback"></param>
        public void SpeechToText(AudioClip _clip, Action<string, AudioClip> _callback, Action<string> _errorCallback = null)
        {
            RequestMicrophonePermission();
            RecognizeSync(_clip, _callback, _errorCallback);
        }

        // 同步识别
        private void RecognizeSync(AudioClip _clip, Action<string, AudioClip> callback, Action<string> _errorCallback = null)
        {
            string url = $"{URLData.URLSSTPath}/recognition";
            byte[] audioFile = WavUtility.FromAudioClip(_clip);
            HttpRequestManager.Instance.Post(url, audioFile,
                response =>
                {
                    try
                    {
                        if (response.result == UnityWebRequest.Result.ConnectionError ||
                        response.result == UnityWebRequest.Result.ProtocolError)
                        {
                            _errorCallback?.Invoke(response.error);
                        }
                        else
                        {
                            var res = JsonMapper.ToObject<ASRResponse>(response.downloadHandler.text);
                            XMDebug.Log(this, $"RecognizeSync response {response.downloadHandler.text}");

                            if (res.code == 0)
                            {
                                callback?.Invoke(res.data.text, _clip);
                            }
                            else
                            {
                                _errorCallback?.Invoke(res.msg);
                                XMDebug.LogError(this, $"ASR Error: {res.msg}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        //_errorCallback?.Invoke(ex.Message);
                        XMDebug.LogError(this, $"ASR Error: {ex.Message}");
                    }
                },
                (URLConstant.URLContentType, URLConstant.URLContentTypeValueAudioWav)
            );
        }
    }
}
