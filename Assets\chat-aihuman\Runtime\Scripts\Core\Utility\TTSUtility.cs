using System;
using System.Text;
using QFramework;
using UnityEngine;
using LitJson;
using XM.Common;
using BestHTTP.WebSocket;


namespace XM.Core
{
    public class TTSUtility : IUtility
    {
        private string ServerUrl = "ws://infer-ds.chengdu-4090-1.edge.shinemo.com:48001/tts/streaming";
        //private string ServerUrl = "http://116.148.216.90:34100/recognition";
        

        private WebSocket _webSocket;
        Action<byte[], long, bool> speakActionBack;
        Action<string> errorActionBack;

        /// <summary>
        /// WS说话
        /// </summary>
        /// <param name="_msg">说话的文本</param>
        /// <param name="textToSpeechId">说话的Id</param>
        /// <param name="_callback">回调</param>
        /// <param name="_errorCallback">错误回调</param>
        public void WSSpeak(string _msg, long textToSpeechId, Action<byte[], long, bool> _callback, Action<string> _errorCallback = null)
        {
            _msg = StringTools.RemoveSpecialCharacter(_msg);
            // 创建请求数据
            var requestData = new RequestData
            {
                text = _msg,
                request_id = textToSpeechId
            };

            string jsonData = JsonMapper.ToJson(requestData);
            jsonData = StringTools.TransitionJsonFormat(jsonData);


            XMDebug.Log(this, $"WSSpeak jsonData {jsonData}");

            speakActionBack = _callback;
            errorActionBack = _errorCallback;

            if (_webSocket == null)
            {
                WSInit();
                _webSocket.OnOpen = ws => { _webSocket.Send(jsonData); };

            }
            else
            {
                _webSocket.Send(jsonData);
            }
        }
        /// <summary>
        /// WS关闭
        /// </summary>
        public void WSClose(Action onClose)
        {
            if (_webSocket != null)
            {
                _webSocket.OnInternalRequestCreated = null;
                _webSocket.OnError = null;
                _webSocket.OnMessage = null;
                _webSocket.OnOpen = null;

                _webSocket.OnClosed = (ws, u16, str) =>
                {
                    onClose?.Invoke();
                    _webSocket = null;
                };
                _webSocket.Close();
                speakActionBack = null;
                errorActionBack = null;
            }
        }
        /// <summary>
        /// WS初始化
        /// </summary>
        private void WSInit()
        {
            XMDebug.Log(this, $"WSInit");

            _webSocket = new WebSocket(new Uri(ServerUrl));
            _webSocket.OnInternalRequestCreated = (ws, req) =>
            {
                req.SetHeader("Accept", "application/json");
                req.SetHeader("Content-Type", "application/json");
            };
            _webSocket.OnMessage = WSOnMessage;
            _webSocket.OnError = WSOnError;

            _webSocket.Open();
        }
        /// <summary>
        /// WS接收消息
        /// </summary>
        /// <param name="ws"></param>
        /// <param name="msg"></param>
        private void WSOnMessage(WebSocket ws, string msg)
        {
            XMDebug.Log(this, $"WSOnMessage msg:{msg}");
            XMSEEEvent seeEvent = XMSEETools.GetXMSEERequestToWS(msg);


            if (seeEvent.eventType == "done")
            {
                speakActionBack.Invoke(null, seeEvent.request_id, true);
            }
            else if (seeEvent.eventType == "audio")
            {
                ProcessAudioData(null, seeEvent.data, seeEvent.request_id, speakActionBack);
            }
        }

        /// <summary>
        /// WS接收错误
        /// </summary>
        /// <param name="ws"></param>
        /// <param name="msg"></param>
        private void WSOnError(WebSocket ws, string msg)
        {
            XMDebug.LogError(this, $"WSOnError msg:{msg}");
            errorActionBack?.Invoke(msg);
        }


        void ProcessAudioData(byte[] audioData, string txt, long textToSpeechId, Action<byte[], long, bool> _callback)
        {
            // 检查是否是WAV格式（可能需要根据实际返回格式调整）
            if (audioData != null && IsWavFormat(audioData))
            {
                // 使用NAudio或其他库处理WAV格式
                PlayWavAudio(audioData);
            }
            else
            {
                // 假设是原始PCM数据（16位，单声道，24000Hz）
                PlayRawPCM(audioData, txt, textToSpeechId, _callback);
            }
        }

        void PlayWavAudio(byte[] wavData)
        {
            // 这里需要实现WAV解析逻辑
            // 可以使用第三方库如NAudio，或Unity的WAV解析工具

            // 临时解决方案：尝试直接加载为AudioClip
            // 注意：这只有在Unity能识别WAV头时才有效
        }

        void PlayRawPCM(byte[] pcmData, string txt, long textToSpeechId, Action<byte[], long, bool> _callback)
        {
            try
            {
                string base64Chunk = string.IsNullOrEmpty(txt) ? Encoding.UTF8.GetString(pcmData) : Encoding.UTF8.GetString(Encoding.Default.GetBytes(txt));
                if (!string.IsNullOrEmpty(base64Chunk))
                {
                    byte[] audioChunk = Convert.FromBase64String(base64Chunk);
                    if (_callback != null)
                    {
                        _callback.Invoke(audioChunk, textToSpeechId, false);
                    }
                }
            }
            catch (System.Exception e)
            {
                XMDebug.LogError(this, $"PCM播放失败: {e.Message} base64Chunk {txt}");
                var file = System.IO.File.Create(Application.dataPath + "/../Pcm/" + DateTimeTool.GetMilliseconds(DateTime.Now).ToString() + ".txt");
                file.Write(Encoding.Default.GetBytes(txt));
            }

        }

        bool IsWavFormat(byte[] data)
        {
            // 简单的WAV格式检查（检查RIFF头）
            return data.Length > 12 &&
                   data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F' &&
                   data[8] == 'W' && data[9] == 'A' && data[10] == 'V' && data[11] == 'E';
        }
        ~TTSUtility() => WSClose(null);

        [Serializable]
        public class RequestData
        {
            public string text;
            public long request_id;
        }
    }
}
